#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Compute gradient-based attribution (saliency & Integrated Gradients) for PaliGemma.

Key features in this version:
- Target bound to the actual generated answer (answer_nll / token_logit / prompt_nll)
- Signed aggregation (preserve positive/negative contributions) + diverging colormap
- Integrated Gradients with trapezoidal rule and optional completeness check
- Reverse pre-processing geometry mapping (resize + center-crop inverse) for accurate overlay
- Precision control (bf16/fp32) and deterministic seed
- Save raw attributions (.npy) + overlay PNG + meta.json

Usage example:
  source /home/<USER>/software/anaconda3/bin/activate spatialvla
  export CUDA_VISIBLE_DEVICES=2
  python compute_gradients.py \
    --model_path /home/<USER>/dataset/X/models/PaliGemma \
    --image_path /home/<USER>/dataset/X/coke.png \
    --prompt "<image>where is the coke can?" \
    --method ig \
    --steps 50 \
    --target_mode answer_nll \
    --agg sum \
    --cmap_signed bwr \
    --completeness_check \
    --output_dir X_input/Gradients/results \
    --device cuda:0
"""
from __future__ import annotations

import argparse
import os
import json
from datetime import datetime
from typing import Tuple, Dict, Any

import numpy as np
import torch
import torch.nn.functional as F
from PIL import Image
import matplotlib.pyplot as plt
from transformers import AutoProcessor, PaliGemmaForConditionalGeneration


# -------------------------- Utils & IO --------------------------

def set_seed(seed: int | None):
    if seed is None:
        return
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
    try:
        import random
        random.seed(seed)
        import numpy as _np
        _np.random.seed(seed)
    except Exception:
        pass
    # Deterministic cuDNN (may slow down)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def _ensure_dir(path: str):
    os.makedirs(path, exist_ok=True)


def _to_numpy(x: torch.Tensor):
    return x.detach().cpu().numpy()


def _normalize01(x: np.ndarray, eps: float = 1e-8) -> np.ndarray:
    x = x - x.min()
    m = x.max()
    if m < eps:
        return np.zeros_like(x)
    return x / (m + eps)


def _normalize_signed_to01(x: np.ndarray, eps: float = 1e-8) -> np.ndarray:
    """
    Map signed values to [0,1] using max-abs normalization around 0.
    0 -> 0.5, negatives -> (0,0.5), positives -> (0.5,1).
    """
    a = float(np.max(np.abs(x)))
    if a < eps:
        return np.full_like(x, 0.5, dtype=np.float32)
    x01 = 0.5 + 0.5 * (x / (a + eps))
    return np.clip(x01, 0.0, 1.0).astype(np.float32)


def _overlay_heatmap_on_image(
    img: Image.Image,
    heatmap01: np.ndarray,
    alpha: float = 0.6,
    cmap: str = "inferno",
) -> Image.Image:
    """Overlay a [0,1] heatmap on a PIL image using a matplotlib colormap."""
    heatmap01 = np.clip(heatmap01, 0.0, 1.0)
    cm = plt.get_cmap(cmap)
    heat_rgba = (cm(heatmap01) * 255).astype(np.uint8)  # (H, W, 4)
    heat_rgb = Image.fromarray(heat_rgba[:, :, :3])
    heat_rgb = heat_rgb.resize(img.size, resample=Image.BILINEAR)
    return Image.blend(img.convert("RGB"), heat_rgb.convert("RGB"), alpha)


def _aggregate_channel_grad(grad: torch.Tensor, mode: str = "l2") -> torch.Tensor:
    """
    grad: (B, C, H, W)
    Returns: (B, H, W)
    """
    if mode == "l1":
        sal = grad.abs().mean(dim=1)
    elif mode == "l2":
        sal = torch.sqrt((grad ** 2).sum(dim=1) + 1e-12)
    elif mode == "sum":          # signed sum to preserve positive/negative contributions
        sal = grad.sum(dim=1)
    elif mode == "abs_sum":      # unsigned sum across channels
        sal = grad.abs().sum(dim=1)
    else:
        raise ValueError(f"Unsupported agg mode: {mode}")
    return sal


# -------------------------- Processor geometry (inverse mapping) --------------------------

def _get_processor_geometry(processor) -> Dict[str, Any]:
    """
    Try to extract geometry info from the processor's image processor.
    Works with most ViT/CLIP-like processors:
      - do_resize (bool), size (int or dict)
      - do_center_crop (bool), crop_size (int or dict)
    """
    ip = getattr(processor, "image_processor", None)
    out = {
        "do_resize": False,
        "size": None,
        "do_center_crop": False,
        "crop_size": None,
    }
    if ip is None:
        return out

    # booleans
    out["do_resize"] = bool(getattr(ip, "do_resize", False))
    out["do_center_crop"] = bool(getattr(ip, "do_center_crop", False))

    # size could be int or dict like {'shortest_edge': 224} or {'height': 224, 'width': 224}
    size = getattr(ip, "size", None)
    crop_size = getattr(ip, "crop_size", None)
    out["size"] = size
    out["crop_size"] = crop_size
    return out


def _compute_resize_dims(orig_w: int, orig_h: int, size) -> Tuple[int, int]:
    """
    Given original (W,H) and processor.size, compute resized (W',H')
    for standard "resize shortest edge -> size" or explicit (H,W).
    """
    if isinstance(size, int):
        # shortest edge -> size
        short = min(orig_w, orig_h)
        if short == 0:
            return orig_w, orig_h
        scale = float(size) / float(short)
        return int(round(orig_w * scale)), int(round(orig_h * scale))

    if isinstance(size, dict):
        if "shortest_edge" in size:
            target = int(size["shortest_edge"])
            short = min(orig_w, orig_h)
            if short == 0:
                return orig_w, orig_h
            scale = float(target) / float(short)
            return int(round(orig_w * scale)), int(round(orig_h * scale))
        # explicit height/width
        tw = int(size.get("width", 0))
        th = int(size.get("height", 0))
        if tw > 0 and th > 0:
            return tw, th

    # fallback: no change
    return orig_w, orig_h


def _compute_crop_dims(resize_w: int, resize_h: int, crop_size) -> Tuple[int, int]:
    if crop_size is None:
        return resize_w, resize_h
    if isinstance(crop_size, int):
        return int(crop_size), int(crop_size)
    if isinstance(crop_size, dict):
        h = int(crop_size.get("height", resize_h))
        w = int(crop_size.get("width", resize_w))
        return w, h
    return resize_w, resize_h


def _inverse_map_heatmap(
    original_img: Image.Image,
    heatmap_proc: np.ndarray,          # (Hp, Wp) - the processor-space heatmap (often crop_size)
    processor,
) -> np.ndarray:
    """
    Map processor-space heatmap back to original image geometry by inverting
    a common pipeline: resize (shortest-edge) -> center_crop.
    If processor metadata is unavailable, falls back to a simple resize.
    """
    geom = _get_processor_geometry(processor)
    W0, H0 = original_img.size  # PIL: (W,H)
    Hp, Wp = heatmap_proc.shape

    # If no geometry, fallback to direct resize
    if not geom["do_resize"] and not geom["do_center_crop"]:
        return np.array(Image.fromarray((heatmap_proc * 255).astype(np.uint8)).resize((W0, H0), Image.BILINEAR)) / 255.0

    # 1) compute resized dims
    Wr, Hr = _compute_resize_dims(W0, H0, geom["size"])
    # 2) place crop back into resized canvas at center
    Wc, Hc = _compute_crop_dims(Wr, Hr, geom["crop_size"])
    # guard
    Wc = min(Wc, Wr)
    Hc = min(Hc, Hr)
    # offsets for center crop
    left = max((Wr - Wc) // 2, 0)
    top = max((Hr - Hc) // 2, 0)

    # Create resized canvas and paste cropped heatmap back
    canvas = np.zeros((Hr, Wr), dtype=np.float32)
    # resize heatmap to crop size (Wc,Hc)
    heat_crop = Image.fromarray((heatmap_proc * 255).astype(np.uint8)).resize((Wc, Hc), Image.BILINEAR)
    heat_crop = np.array(heat_crop).astype(np.float32) / 255.0
    canvas[top:top + Hc, left:left + Wc] = heat_crop

    # 3) resize canvas back to original size
    canvas_img = Image.fromarray((canvas * 255).astype(np.uint8)).resize((W0, H0), Image.BILINEAR)
    return np.array(canvas_img).astype(np.float32) / 255.0


# -------------------------- Data preparation --------------------------

def _prepare_inputs(processor, image_path: str, prompt: str, device: str):
    image = Image.open(image_path).convert("RGB")
    inputs = processor(text=prompt, images=image, return_tensors="pt")
    # move tensors to device
    inputs = {k: v.to(device) if hasattr(v, "to") else v for k, v in inputs.items()}
    return image, inputs


# -------------------------- Target construction & loss closures --------------------------

@torch.no_grad()
def _generate_answer(model, inputs, processor, max_new_tokens: int = 32, seed: int | None = None):
    """
    Generate answer tokens once and reuse during attribution, so we attribute
    to a fixed output. Returns (ans_ids, ans_text). No gradients here.
    """
    if seed is not None:
        set_seed(seed)
    gen_kwargs = {"max_new_tokens": max_new_tokens, "do_sample": False}
    gen_ids = model.generate(**inputs, **gen_kwargs)
    prompt_len = inputs["input_ids"].shape[1]
    ans_ids = gen_ids[:, prompt_len:]
    try:
        ans_text = processor.batch_decode(ans_ids, skip_special_tokens=True)[0]
    except Exception:
        ans_text = ""
    return ans_ids, ans_text


def _build_target_fn(model, base_inputs, processor, args):
    """
    Build a closure loss_fn(pixel_values) -> (loss_scalar, meta_dict)

    Supported target modes:
      - answer_nll: NLL of the generated answer tokens (default; recommended)
      - prompt_nll: NLL of reproducing the prompt (legacy behavior)
      - token_logit: negative log-prob of the last generated token
    """
    target_mode = getattr(args, "target_mode", "answer_nll")
    device = base_inputs["input_ids"].device

    ans_ids = None
    ans_text = None
    if target_mode in ("answer_nll", "token_logit"):
        ans_ids, ans_text = _generate_answer(
            model,
            base_inputs,
            processor,
            max_new_tokens=getattr(args, "gen_max_new_tokens", 32),
            seed=getattr(args, "seed", None),
        )

    def loss_fn(pixel_values: torch.Tensor):
        if target_mode == "prompt_nll":
            outputs = model(
                input_ids=base_inputs["input_ids"],
                pixel_values=pixel_values,
                labels=base_inputs["input_ids"],
            )
            return outputs.loss, {"target": "prompt_nll"}

        # Fallback to prompt_nll if no answer
        if ans_ids is None or ans_ids.numel() == 0:
            outputs = model(
                input_ids=base_inputs["input_ids"],
                pixel_values=pixel_values,
                labels=base_inputs["input_ids"],
            )
            return outputs.loss, {"target": "fallback_prompt_nll"}

        prompt_ids = base_inputs["input_ids"]
        B = prompt_ids.shape[0]
        assert B == 1, "Current implementation assumes batch size 1 for attribution"

        if target_mode == "answer_nll":
            # Teacher-forced scoring of the generated answer
            inp = torch.cat([prompt_ids, ans_ids.to(device)], dim=1)
            ignore = torch.full_like(prompt_ids, -100)
            labels = torch.cat([ignore, ans_ids.to(device)], dim=1)
            outputs = model(input_ids=inp, pixel_values=pixel_values, labels=labels)
            return outputs.loss, {"target": "answer_nll", "answer_text": ans_text}

        if target_mode == "token_logit":
            # Score the last generated token log-prob (must keep gradient!)
            if ans_ids.shape[1] > 1:
                inp = torch.cat([prompt_ids, ans_ids[:, :-1].to(device)], dim=1)
            else:
                inp = prompt_ids
            out = model(input_ids=inp, pixel_values=pixel_values)  # keep grad
            logits = out.logits.float()  # (B, T, V)
            target_id = int(ans_ids[0, -1].item())
            log_probs = F.log_softmax(logits[0, -1], dim=-1)
            loss = -log_probs[target_id]
            return loss, {"target": "token_logit", "answer_text": ans_text, "target_token_id": target_id}

        # Default fallback
        outputs = model(
            input_ids=base_inputs["input_ids"],
            pixel_values=pixel_values,
            labels=base_inputs["input_ids"],
        )
        return outputs.loss, {"target": "prompt_nll_default"}

    return loss_fn, {"target_mode": target_mode, "answer_text": ans_text}


# -------------------------- Saliency & Integrated Gradients --------------------------

def _saliency(model, inputs, processor, args, agg_mode: str = "l2"):
    """
    Vanilla gradient saliency on pixel_values.
    """
    pixel = inputs["pixel_values"].to(torch.float32).requires_grad_(True)
    local_inputs = {**inputs, "pixel_values": pixel}

    loss_fn, target_meta = _build_target_fn(model, local_inputs, processor, args)

    # Ensure no autocast to keep stable grads if requested
    with torch.autocast(device_type="cuda", enabled=False) if str(args.device).startswith("cuda") else torch.cpu.amp.autocast(enabled=False):
        model.zero_grad(set_to_none=True)
        loss, meta = loss_fn(pixel)
        loss.backward()

    grad = pixel.grad  # (B, C, H, W)
    sal = _aggregate_channel_grad(grad, mode=agg_mode)  # (B, H, W)
    return sal, float(loss.item()), (target_meta | meta)


def _make_baseline(pixel: torch.Tensor, kind: str = "zero") -> torch.Tensor:
    if kind == "mean":
        mean_vals = pixel.mean(dim=(2, 3), keepdim=True)
        baseline = torch.zeros_like(pixel) + mean_vals
    else:
        baseline = torch.zeros_like(pixel)
    return baseline


def _integrated_gradients(model, inputs, processor, args, steps: int = 20, baseline_kind: str = "zero", agg_mode: str = "l2"):
    pixel = inputs["pixel_values"].to(torch.float32)
    baseline = _make_baseline(pixel, baseline_kind)

    total_grad = torch.zeros_like(pixel)
    base_inputs = {**inputs}
    loss_fn, target_meta = _build_target_fn(model, base_inputs, processor, args)

    use_trapezoid = getattr(args, "integral_rule", "mean") == "trapezoid"
    weights_sum = 0.0

    # Optional completeness check
    base_loss_val = None
    inp_loss_val = None
    if getattr(args, "completeness_check", False):
        with torch.no_grad():
            bl, _ = loss_fn(baseline.detach())
            il, _ = loss_fn(pixel.detach())
        base_loss_val = float(bl.item())
        inp_loss_val = float(il.item())

    # IG loop
    for i in range(1, steps + 1):
        alpha = float(i) / float(steps)
        x = baseline + alpha * (pixel - baseline)
        x.requires_grad_(True)

        with torch.autocast(device_type="cuda", enabled=False) if str(args.device).startswith("cuda") else torch.cpu.amp.autocast(enabled=False):
            loss, meta = loss_fn(x)
            loss.backward()

        if x.grad is not None:
            if use_trapezoid:
                w = 1.0 if (i != 1 and i != steps) else 0.5
                total_grad = total_grad + w * x.grad
                weights_sum += w
            else:
                total_grad = total_grad + x.grad
        model.zero_grad(set_to_none=True)

    avg_grad = total_grad / (weights_sum if (use_trapezoid and weights_sum > 0) else float(steps))
    ig = (pixel - baseline) * avg_grad  # (B, C, H, W)
    sal = _aggregate_channel_grad(ig, mode=agg_mode)

    info = target_meta | meta
    if getattr(args, "completeness_check", False):
        ig_sum = float(ig.sum().item())
        target_diff = None if (base_loss_val is None or inp_loss_val is None) else (inp_loss_val - base_loss_val)
        comp_err = None if target_diff is None else float(abs(ig_sum - target_diff) / (abs(target_diff) + 1e-8))
        info = info | {"ig_sum": ig_sum, "target_diff": target_diff, "completeness_error": comp_err}

    return sal, info


# -------------------------- Main --------------------------

def main():
    parser = argparse.ArgumentParser(description="Gradient-based attribution for PaliGemma")
    parser.add_argument("--model_path", type=str, required=True)
    parser.add_argument("--image_path", type=str, required=True)
    parser.add_argument("--prompt", type=str, required=True, help="Text prompt (should include <image> placeholder)")
    parser.add_argument("--method", type=str, choices=["saliency", "ig"], default="saliency")
    parser.add_argument("--output_dir", type=str, default="grad_results")
    parser.add_argument("--device", type=str, default="cuda:0")

    # IG & saliency settings
    parser.add_argument("--steps", type=int, default=20, help="Steps for Integrated Gradients")
    parser.add_argument("--baseline", type=str, choices=["zero", "mean"], default="zero", help="Baseline for IG")
    parser.add_argument("--agg", type=str, choices=["l1", "l2", "sum", "abs_sum"], default="l2", help="Aggregation over channels")

    # Target & generation settings
    parser.add_argument("--target_mode", type=str, choices=["answer_nll", "prompt_nll", "token_logit"], default="answer_nll")
    parser.add_argument("--gen_max_new_tokens", type=int, default=32)
    parser.add_argument("--seed", type=int, default=None)

    # Visualization
    parser.add_argument("--alpha", type=float, default=0.6, help="Overlay alpha")
    parser.add_argument("--cmap", type=str, default="inferno", help="Colormap for unsigned heatmaps")
    parser.add_argument("--cmap_signed", type=str, default="bwr", help="Colormap for signed heatmaps (e.g., agg=sum)")
    parser.add_argument("--save_raw", action="store_true", help="Save raw attribution array before normalization")

    # Integration rule & checks
    parser.add_argument("--integral_rule", type=str, choices=["mean", "trapezoid"], default="mean")
    parser.add_argument("--completeness_check", action="store_true")

    # Precision
    parser.add_argument("--precision", type=str, choices=["bf16", "fp32"], default="bf16", help="Model weights dtype for loading")

    args = parser.parse_args()

    set_seed(args.seed)

    # Prepare model & processor
    dtype = torch.bfloat16 if args.precision == "bf16" else torch.float32
    device = torch.device(args.device)

    model = PaliGemmaForConditionalGeneration.from_pretrained(
        args.model_path,
        torch_dtype=dtype,
    ).eval().to(device)

    processor = AutoProcessor.from_pretrained(args.model_path)
    model.config.return_dict = True

    # Prepare data
    try:
        image, inputs = _prepare_inputs(processor, args.image_path, args.prompt, device)
    except FileNotFoundError as e:
        print(f"[Error] Image not found: {e}")
        return

    # Run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    out_dir = os.path.join(args.output_dir, timestamp)
    _ensure_dir(out_dir)

    try:
        if args.method == "saliency":
            sal, loss_val, target_info = _saliency(model, inputs, processor, args, agg_mode=args.agg)
            sal = sal[0].detach().cpu().float()  # (H, W)
            method_tag = "saliency"
        else:
            sal, target_info = _integrated_gradients(
                model, inputs, processor, args,
                steps=args.steps, baseline_kind=args.baseline, agg_mode=args.agg
            )
            sal = sal[0].detach().cpu().float()
            loss_val = None
            method_tag = "ig"
    except RuntimeError as e:
        msg = str(e).lower()
        if "out of memory" in msg or "cuda" in msg and "oom" in msg:
            print("[CUDA OOM] Try reducing --steps (for IG), using --precision fp32 offload cautiously, "
                  "or resizing the input image.")
        else:
            print(f"[Runtime error] {e}")
        return

    # ----- Normalization (signed vs unsigned) -----
    sal_np = sal.numpy()
    signed = (args.agg == "sum")
    if signed:
        sal01 = _normalize_signed_to01(sal_np)
        cmap_to_use = args.cmap_signed
    else:
        sal01 = _normalize01(sal_np)
        cmap_to_use = args.cmap

    # ----- Geometry inverse mapping (resize + center-crop) -----
    sal_mapped = _inverse_map_heatmap(image, sal01, processor)  # (H0, W0) in [0,1]

    # Overlay
    overlay = _overlay_heatmap_on_image(image, sal_mapped, alpha=args.alpha, cmap=cmap_to_use)

    # Save outputs
    if args.save_raw:
        np.save(os.path.join(out_dir, f"{method_tag}_raw.npy"), sal_np.astype(np.float32))
    np.save(os.path.join(out_dir, f"{method_tag}.npy"), sal_mapped.astype(np.float32))
    overlay.save(os.path.join(out_dir, f"{method_tag}_overlay.png"))

    # Meta
    meta = {
        "model_path": args.model_path,
        "image_path": args.image_path,
        "prompt": args.prompt,
        "method": args.method,
        "steps": args.steps,
        "baseline": args.baseline,
        "agg": args.agg,
        "alpha": args.alpha,
        "cmap": args.cmap,
        "cmap_signed": args.cmap_signed,
        "timestamp": timestamp,
        "loss": loss_val,
        "device": args.device,
        "target_mode": getattr(args, "target_mode", None),
        "target_info": target_info if 'target_info' in locals() else None,
        "integral_rule": getattr(args, "integral_rule", None),
        "precision": args.precision,
        "seed": args.seed,
    }

    # Optional: completeness diagnostics
    if args.completeness_check and target_info is not None:
        ce = target_info.get("completeness_error", None)
        if ce is not None:
            meta["completeness_error"] = ce
            meta["ig_sum"] = target_info.get("ig_sum", None)
            meta["target_diff"] = target_info.get("target_diff", None)
            warn = "OK"
            if ce > 0.05:
                warn = "WARN>5%"
            print(f"[Completeness] relative error: {ce:.4e}  ({warn})")

    with open(os.path.join(out_dir, "meta.json"), "w") as f:
        json.dump(meta, f, indent=2, ensure_ascii=False)

    print("=== Gradient attribution done ===")
    print(f"Saved to: {out_dir}")


if __name__ == "__main__":
    main()
