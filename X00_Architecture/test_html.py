#!/usr/bin/env python3
"""
测试生成的HTML文件
"""

import os
import json

def test_html_file():
    """测试HTML文件"""
    html_file = "/home/<USER>/dataset/X/X00_Architecture/paligemma_architecture_visualization.html"
    analysis_file = "/home/<USER>/dataset/X/X00_Architecture/architecture_analysis.json"
    
    print("🔍 测试生成的文件...")
    
    # 检查文件是否存在
    if not os.path.exists(html_file):
        print(f"❌ HTML文件不存在: {html_file}")
        return False
    
    if not os.path.exists(analysis_file):
        print(f"❌ 分析文件不存在: {analysis_file}")
        return False
    
    # 检查文件大小
    html_size = os.path.getsize(html_file) / (1024 * 1024)  # MB
    analysis_size = os.path.getsize(analysis_file) / (1024 * 1024)  # MB
    
    print(f"✅ HTML文件大小: {html_size:.2f} MB")
    print(f"✅ 分析文件大小: {analysis_size:.2f} MB")
    
    # 检查分析结果
    try:
        with open(analysis_file, 'r', encoding='utf-8') as f:
            analysis_data = json.load(f)
        
        metadata = analysis_data.get('metadata', {})
        print(f"✅ 分析时间: {metadata.get('analysis_time', 'N/A')}")
        print(f"✅ 总节点数: {metadata.get('total_nodes', 'N/A')}")
        print(f"✅ 总边数: {metadata.get('total_edges', 'N/A')}")
        
        # 检查组件统计
        component_stats = analysis_data.get('component_stats', {})
        print("\n📊 组件统计:")
        for component, stats in component_stats.items():
            print(f"  {component}: {stats['node_count']}个节点, {stats['total_params']:,}个参数")
        
        # 检查推理结果
        inference_result = analysis_data.get('inference_result', {})
        if inference_result.get('success', False):
            output_text = inference_result.get('output', {}).get('generated_text', 'N/A')
            print(f"✅ 推理成功，生成文本: '{output_text}'")
        else:
            print("❌ 推理失败")
        
    except Exception as e:
        print(f"❌ 读取分析文件失败: {e}")
        return False
    
    # 检查HTML文件内容
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 检查关键内容
        if 'PaliGemma 深度架构可视化' in html_content:
            print("✅ HTML标题正确")
        else:
            print("❌ HTML标题缺失")
            return False
        
        if 'architectureData' in html_content:
            print("✅ 架构数据已嵌入")
        else:
            print("❌ 架构数据缺失")
            return False
        
        if 'd3js.org/d3' in html_content:
            print("✅ D3.js库已引用")
        else:
            print("❌ D3.js库缺失")
            return False
        
        if 'data:image/png;base64' in html_content:
            print("✅ 输入图像已嵌入")
        else:
            print("❌ 输入图像缺失")
            return False
        
    except Exception as e:
        print(f"❌ 读取HTML文件失败: {e}")
        return False
    
    print("\n🎉 所有测试通过！HTML文件生成成功！")
    print(f"\n📁 HTML文件路径: {html_file}")
    print("💡 请在浏览器中打开此文件查看可视化效果")
    
    return True

if __name__ == "__main__":
    test_html_file()