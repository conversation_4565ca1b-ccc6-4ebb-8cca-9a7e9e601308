# PaliGemma 深度架构可视化系统

这是一个全面的PaliGemma模型架构可视化工具，能够深入到每个计算单元的层面，展示模型内部的数据流动过程。

## 功能特性

### 🔍 深度架构分析
- **细粒度分析**：深入到每个Linear层、LayerNorm、Attention等最小计算单元
- **实时数据捕获**：通过PyTorch hooks捕获推理过程中的实际数据流
- **参数统计**：详细记录每个模块的参数数量、形状和统计信息
- **执行追踪**：记录模块的执行顺序和数据流向

### 🎨 交互式可视化
- **分层布局**：Vision Encoder → Projection → Language Model 的清晰展示
- **节点交互**：点击节点查看详细参数信息，悬停显示快速预览
- **数据流动画**：播放数据在模型中的流动过程
- **多视图模式**：概览模式、详细模式、执行顺序模式
- **响应式设计**：支持桌面和移动设备

### 📊 详细信息展示
- **模块参数**：权重形状、参数数量、统计信息
- **数据流**：输入输出形状、张量统计、形状变化
- **执行信息**：执行顺序、计算复杂度
- **组件统计**：各组件的节点数量和参数分布

## 文件结构

```
X00_Architecture/
├── README.md                           # 本文件
├── run_complete_visualization.py       # 主运行脚本
├── deep_architecture_analyzer.py       # 深度架构分析器
├── interactive_html_generator.py       # HTML生成器
├── architecture_analysis.json          # 分析结果（运行后生成）
└── paligemma_architecture_visualization.html  # 最终HTML文件（运行后生成）
```

## 环境要求

### 系统要求
- Python 3.7+
- CUDA支持的GPU（推荐）
- 足够的内存（推荐16GB+）

### 依赖包
```bash
pip install torch transformers pillow numpy matplotlib
```

### Conda环境
```bash
source /home/<USER>/software/anaconda3/bin/activate spatialvla
```

## 使用方法

### 1. 基本使用

使用默认参数运行完整分析：

```bash
cd /home/<USER>/dataset/X/X00_Architecture
python run_complete_visualization.py
```

这将使用以下默认设置：
- 模型路径: `/home/<USER>/dataset/X/models/PaliGemma`
- 输入图像: `/home/<USER>/dataset/X/coke.png`
- 文本提示: `"pick coke can"`
- 输出目录: `/home/<USER>/dataset/X/X00_Architecture`

### 2. 自定义参数

```bash
python run_complete_visualization.py \
    --model_path /path/to/your/model \
    --image_path /path/to/your/image.png \
    --text_prompt "your custom prompt" \
    --output_dir /path/to/output \
    --device cuda:0
```

### 3. 跳过分析，仅生成HTML

如果已有分析结果，可以跳过分析步骤：

```bash
python run_complete_visualization.py --skip_analysis
```

### 4. 单独运行组件

#### 仅运行架构分析
```python
from deep_architecture_analyzer import DeepArchitectureAnalyzer

analyzer = DeepArchitectureAnalyzer("/path/to/model")
result = analyzer.analyze_architecture("/path/to/image.png", "text prompt")
```

#### 仅生成HTML
```python
from interactive_html_generator import InteractiveHTMLGenerator
import json

with open('architecture_analysis.json', 'r') as f:
    analysis_result = json.load(f)

generator = InteractiveHTMLGenerator()
generator.generate_html(analysis_result, 'output.html')
```

## 输出文件说明

### 1. architecture_analysis.json
包含完整的架构分析结果：
- **metadata**: 分析元信息（模型路径、输入信息、时间戳等）
- **nodes**: 所有计算节点的详细信息
- **edges**: 数据流边的信息
- **component_stats**: 各组件的统计信息
- **hierarchy**: 层级结构信息
- **data_flow**: 实际数据流信息

### 2. paligemma_architecture_visualization.html
交互式可视化HTML文件，包含：
- 完整的模型架构图
- 交互式节点和边
- 控制面板和信息面板
- 内嵌的CSS和JavaScript
- Base64编码的输入图像

## 可视化界面说明

### 主要区域
1. **标题栏**：显示工具名称和描述
2. **控制面板**：输入信息、统计数据、控制按钮
3. **可视化区域**：主要的架构图显示区域
4. **信息面板**：显示选中节点的详细信息
5. **图例**：颜色编码说明

### 交互功能
- **缩放和平移**：鼠标滚轮缩放，拖拽平移视图
- **节点点击**：点击节点查看详细信息
- **节点悬停**：悬停显示快速预览
- **数据流动画**：点击"播放数据流"按钮
- **视图切换**：在不同视图模式间切换
- **标签切换**：显示/隐藏节点标签

### 颜色编码
- **蓝色**：Vision Encoder组件
- **红色**：Projection组件  
- **绿色**：Language Model组件
- **灰色**：其他组件

### 节点大小
节点大小表示参数数量：
- 小圆圈：参数少的模块（如激活函数）
- 大圆圈：参数多的模块（如大型Linear层）

## 技术原理

### 架构分析
1. **模块遍历**：递归遍历模型的所有子模块
2. **Hook注册**：为每个叶子模块注册forward hook
3. **数据捕获**：在推理过程中捕获输入输出张量
4. **统计计算**：计算张量的形状、统计信息等

### 可视化技术
- **D3.js**：用于创建交互式图形
- **力导向布局**：自动排列节点位置
- **SVG渲染**：矢量图形，支持缩放
- **响应式设计**：适配不同屏幕尺寸

### 性能优化
- **采样统计**：对大张量进行采样以提高性能
- **懒加载**：按需加载详细信息
- **数据压缩**：优化JSON数据结构
- **虚拟化**：大量节点时的渲染优化

## 故障排除

### 常见问题

1. **CUDA内存不足**
   ```
   解决方案：
   - 使用CPU: --device cpu
   - 减少batch size
   - 关闭其他GPU程序
   ```

2. **模型加载失败**
   ```
   检查项：
   - 模型路径是否正确
   - 模型文件是否完整
   - 权限是否足够
   ```

3. **Hook错误**
   ```
   可能原因：
   - 模型结构不匹配
   - PyTorch版本不兼容
   - 内存不足
   ```

4. **HTML显示异常**
   ```
   检查项：
   - 浏览器是否支持现代JavaScript
   - 文件是否完整生成
   - 网络连接（如果使用CDN）
   ```

### 调试模式

启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 性能监控

查看内存使用：
```bash
nvidia-smi  # GPU内存
htop        # CPU和系统内存
```

## 扩展功能

### 自定义分析
可以通过修改`DeepArchitectureAnalyzer`来添加自定义分析：

```python
def custom_analysis_hook(module, input, output):
    # 自定义分析逻辑
    pass

# 添加到特定模块
model.some_module.register_forward_hook(custom_analysis_hook)
```

### 自定义可视化
可以通过修改HTML模板来自定义可视化效果：

```javascript
// 自定义节点渲染
function customNodeRenderer(node) {
    // 自定义渲染逻辑
}
```

## 注意事项

1. **内存使用**：分析大模型时会消耗大量内存
2. **存储空间**：生成的文件可能较大（5-10MB）
3. **计算时间**：完整分析可能需要几分钟
4. **浏览器兼容性**：推荐使用现代浏览器（Chrome、Firefox、Safari）

## 更新日志

### v1.0.0 (2024-12-27)
- 初始版本发布
- 支持PaliGemma模型深度分析
- 交互式HTML可视化
- 多视图模式支持

## 许可证

本项目遵循MIT许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个工具。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至项目维护者

---

**享受探索PaliGemma模型内部结构的过程！** 🚀