#!/usr/bin/env python3
"""
交互式HTML生成器
将架构分析结果转换为交互式的HTML可视化
"""

import json
import base64
import os
from typing import Dict, Any, List
from PIL import Image
import io

class InteractiveHTMLGenerator:
    """交互式HTML生成器"""
    
    def __init__(self):
        self.template_dir = os.path.join(os.path.dirname(__file__), 'templates')
        self.static_dir = os.path.join(os.path.dirname(__file__), 'static')
    
    def generate_html(self, analysis_result: Dict[str, Any], output_path: str):
        """生成完整的HTML文件"""
        print("正在生成交互式HTML...")
        
        # 准备数据
        html_data = self._prepare_html_data(analysis_result)
        
        # 生成HTML内容
        html_content = self._generate_html_content(html_data)
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"HTML文件已生成: {output_path}")
        return output_path
    
    def _prepare_html_data(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """准备HTML所需的数据"""
        
        # 转换图像为base64
        image_base64 = self._image_to_base64(analysis_result['metadata']['image_path'])
        
        # 处理节点数据
        nodes_data = self._process_nodes_data(analysis_result['nodes'])
        
        # 处理边数据
        edges_data = self._process_edges_data(analysis_result['edges'])
        
        # 生成统计信息
        stats_data = self._generate_stats_data(analysis_result)
        
        return {
            'metadata': analysis_result['metadata'],
            'image_base64': image_base64,
            'nodes': nodes_data,
            'edges': edges_data,
            'stats': stats_data,
            'inference_result': analysis_result['inference_result'],
            'component_stats': analysis_result['component_stats']
        }
    
    def _image_to_base64(self, image_path: str) -> str:
        """将图像转换为base64编码"""
        try:
            with Image.open(image_path) as img:
                # 调整图像大小以优化显示
                img.thumbnail((400, 400), Image.Resampling.LANCZOS)
                
                # 转换为base64
                buffer = io.BytesIO()
                img.save(buffer, format='PNG')
                img_str = base64.b64encode(buffer.getvalue()).decode()
                return f"data:image/png;base64,{img_str}"
        except Exception as e:
            print(f"图像转换失败: {e}")
            return ""
    
    def _process_nodes_data(self, nodes: List[Dict]) -> List[Dict]:
        """处理节点数据 - 增强版本，添加更多分类信息"""
        processed_nodes = []

        for node in nodes:
            # 确定节点的详细分类
            node_category = self._categorize_node(node['name'], node['module_type'], node['component'])

            processed_node = {
                'id': node['id'],
                'name': node['name'].split('.')[-1],  # 只显示最后一部分
                'fullName': node['name'],
                'type': node['module_type'],
                'component': node['component'],
                'category': node_category,  # 新增：详细分类
                'layer': node.get('layer_index', -1),
                'x': node['position']['x'],
                'y': node['position']['y'],
                'executionOrder': node.get('execution_order', -1),
                'parameters': {
                    'total': node['parameters']['total_params'],
                    'trainable': node['parameters']['trainable_params'],
                    'shapes': node['parameters']['param_shapes']
                },
                'inputShape': node.get('input_shape', []),
                'outputShape': node.get('output_shape', []),
                'dataStats': node.get('data_stats', {}),
                'color': self._get_node_color(node['component'], node['module_type']),
                'size': self._get_node_size(node['parameters']['total_params']),
                'strokeColor': self._get_stroke_color(node_category),  # 新增：边框颜色
                'shape': self._get_node_shape(node_category)  # 新增：节点形状标识
            }
            processed_nodes.append(processed_node)

        return processed_nodes

    def _categorize_node(self, name: str, module_type: str, component: str) -> str:
        """对节点进行详细分类"""
        name_lower = name.lower()
        type_lower = module_type.lower()

        # Vision Encoder 细分
        if component == 'vision':
            if 'patch_embedding' in name_lower or 'conv2d' in type_lower:
                return 'vision_input'
            elif 'attention' in name_lower or 'attn' in type_lower:
                return 'vision_attention'
            elif 'mlp' in name_lower or ('linear' in type_lower and 'fc' in name_lower):
                return 'vision_mlp'
            elif 'norm' in name_lower:
                return 'vision_norm'
            elif 'pooler' in name_lower:
                return 'vision_pooling'
            else:
                return 'vision_other'

        # Projection Layer 细分
        elif component == 'projection':
            if 'multi_modal_projector' in name_lower:
                return 'projection_main'
            elif 'linear' in type_lower:
                return 'projection_linear'
            else:
                return 'projection_other'

        # Language Model 细分
        elif component == 'language':
            if 'embed' in name_lower:
                return 'language_embedding'
            elif 'attention' in name_lower or 'attn' in type_lower:
                return 'language_attention'
            elif 'mlp' in name_lower or 'gate_proj' in name_lower or 'up_proj' in name_lower or 'down_proj' in name_lower:
                return 'language_mlp'
            elif 'norm' in name_lower:
                return 'language_norm'
            elif 'lm_head' in name_lower:
                return 'language_output'
            else:
                return 'language_other'

        else:
            return 'other'

    def _get_stroke_color(self, category: str) -> str:
        """根据分类获取边框颜色"""
        stroke_colors = {
            # Vision 系列
            'vision_input': '#1a5490',
            'vision_attention': '#2471a3',
            'vision_mlp': '#5dade2',
            'vision_norm': '#85c1e9',
            'vision_pooling': '#aed6f1',
            'vision_other': '#d6eaf8',

            # Projection 系列
            'projection_main': '#922b21',
            'projection_linear': '#cb4335',
            'projection_other': '#f1948a',

            # Language 系列
            'language_embedding': '#196f3d',
            'language_attention': '#239b56',
            'language_mlp': '#58d68d',
            'language_norm': '#82e0aa',
            'language_output': '#a9dfbf',
            'language_other': '#d5f4e6',

            # 其他
            'other': '#566573'
        }
        return stroke_colors.get(category, '#566573')

    def _get_node_shape(self, category: str) -> str:
        """根据分类获取节点形状标识"""
        # 返回形状类型，用于在前端渲染不同形状
        shape_mapping = {
            # Vision 系列 - 圆形变体
            'vision_input': 'circle',
            'vision_attention': 'diamond',
            'vision_mlp': 'square',
            'vision_norm': 'triangle',
            'vision_pooling': 'hexagon',
            'vision_other': 'circle',

            # Projection 系列 - 矩形变体
            'projection_main': 'rect',
            'projection_linear': 'rect',
            'projection_other': 'rect',

            # Language 系列 - 多边形变体
            'language_embedding': 'pentagon',
            'language_attention': 'star',
            'language_mlp': 'octagon',
            'language_norm': 'triangle',
            'language_output': 'diamond',
            'language_other': 'circle',

            # 其他
            'other': 'circle'
        }
        return shape_mapping.get(category, 'circle')
    
    def _process_edges_data(self, edges: List[Dict]) -> List[Dict]:
        """处理边数据"""
        processed_edges = []
        
        for edge in edges:
            processed_edge = {
                'id': edge['id'],
                'source': edge['source'],
                'target': edge['target'],
                'type': edge.get('type', 'data_flow'),
                'executionOrder': edge.get('execution_order', -1),
                'dataInfo': edge.get('data_info', {}),
                'width': self._get_edge_width(edge.get('data_info', {}))
            }
            processed_edges.append(processed_edge)
        
        return processed_edges
    
    def _get_node_color(self, component: str, module_type: str) -> str:
        """获取节点颜色 - 增强版本，支持更细致的分类"""
        # 主要组件颜色
        component_colors = {
            'vision': '#3498db',      # 蓝色系 - Vision Encoder
            'projection': '#e74c3c',  # 红色系 - Projection Layer
            'language': '#2ecc71',    # 绿色系 - Language Model
            'other': '#95a5a6'        # 灰色系 - Other
        }

        # 模块类型特定颜色
        module_type_colors = {
            # Vision 相关
            'conv2d': '#2980b9',           # 深蓝 - 卷积层
            'layernorm': '#5dade2',        # 浅蓝 - 归一化层
            'multiheadattention': '#1f4e79', # 深蓝紫 - 注意力层

            # Projection 相关
            'linear': '#c0392b',           # 深红 - 线性层
            'dropout': '#e67e22',          # 橙红 - Dropout层

            # Language 相关
            'embedding': '#27ae60',        # 深绿 - 嵌入层
            'gelu': '#58d68d',            # 浅绿 - 激活函数
            'rmsnorm': '#1e8449',         # 深绿 - RMS归一化

            # 特殊层类型
            'siglipvisionmodel': '#8e44ad', # 紫色 - 特殊视觉模型
            'gemmaforreasoning': '#f39c12', # 金色 - 推理模型
        }

        base_color = component_colors.get(component, '#95a5a6')

        # 优先使用模块类型特定颜色
        module_key = module_type.lower().replace('_', '').replace('-', '')
        if module_key in module_type_colors:
            return module_type_colors[module_key]

        # 根据模块类型关键词调整颜色
        if 'attention' in module_type.lower() or 'attn' in module_type.lower():
            return self._darken_color(base_color, 0.2)  # 注意力层用深色
        elif 'mlp' in module_type.lower() or 'linear' in module_type.lower():
            return self._lighten_color(base_color, 0.2)  # MLP/线性层用浅色
        elif 'norm' in module_type.lower():
            return self._adjust_saturation(base_color, 0.8)  # 归一化层调整饱和度
        elif 'embedding' in module_type.lower():
            return self._darken_color(base_color, 0.3)  # 嵌入层用更深色
        elif 'conv' in module_type.lower():
            return self._adjust_hue(base_color, 15)  # 卷积层调整色相
        else:
            return base_color
    
    def _get_node_size(self, param_count: int) -> float:
        """根据参数数量确定节点大小"""
        if param_count == 0:
            return 8
        elif param_count < 1000:
            return 10
        elif param_count < 100000:
            return 15
        elif param_count < 1000000:
            return 20
        else:
            return 25
    
    def _get_edge_width(self, data_info: Dict) -> float:
        """根据数据流信息确定边的宽度"""
        if not data_info or 'source_output' not in data_info:
            return 1
        
        # 根据张量大小确定边宽度
        source_output = data_info['source_output']
        if source_output and 'shapes' in source_output and source_output['shapes']:
            shape = source_output['shapes'][0]
            if shape:
                total_elements = 1
                for dim in shape:
                    total_elements *= dim
                
                if total_elements < 1000:
                    return 1
                elif total_elements < 100000:
                    return 2
                elif total_elements < 1000000:
                    return 3
                else:
                    return 4
        
        return 1
    
    def _lighten_color(self, hex_color: str, factor: float) -> str:
        """使颜色变浅"""
        # 简单的颜色变浅实现
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        rgb = tuple(min(255, int(c + (255 - c) * factor)) for c in rgb)
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"
    
    def _darken_color(self, hex_color: str, factor: float) -> str:
        """使颜色变深"""
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        rgb = tuple(max(0, int(c * (1 - factor))) for c in rgb)
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"

    def _adjust_saturation(self, hex_color: str, saturation: float) -> str:
        """调整颜色饱和度"""
        import colorsys
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) / 255.0 for i in (0, 2, 4))
        hsv = colorsys.rgb_to_hsv(*rgb)
        hsv = (hsv[0], min(1.0, hsv[1] * saturation), hsv[2])
        rgb = colorsys.hsv_to_rgb(*hsv)
        rgb = tuple(int(c * 255) for c in rgb)
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"

    def _adjust_hue(self, hex_color: str, hue_shift: float) -> str:
        """调整颜色色相"""
        import colorsys
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) / 255.0 for i in (0, 2, 4))
        hsv = colorsys.rgb_to_hsv(*rgb)
        hsv = ((hsv[0] + hue_shift / 360.0) % 1.0, hsv[1], hsv[2])
        rgb = colorsys.hsv_to_rgb(*hsv)
        rgb = tuple(int(c * 255) for c in rgb)
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"
    
    def _generate_stats_data(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成统计数据"""
        nodes = analysis_result['nodes']
        edges = analysis_result['edges']
        
        # 基本统计
        total_params = sum(node['parameters']['total_params'] for node in nodes)
        executed_nodes = len([node for node in nodes if node.get('execution_order', -1) >= 0])
        
        # 组件统计
        component_counts = {}
        for node in nodes:
            component = node['component']
            component_counts[component] = component_counts.get(component, 0) + 1
        
        # 模块类型统计
        module_type_counts = {}
        for node in nodes:
            module_type = node['module_type']
            module_type_counts[module_type] = module_type_counts.get(module_type, 0) + 1
        
        return {
            'totalNodes': len(nodes),
            'totalEdges': len(edges),
            'totalParams': total_params,
            'executedNodes': executed_nodes,
            'componentCounts': component_counts,
            'moduleTypeCounts': module_type_counts
        }
    
    def _generate_html_content(self, html_data: Dict[str, Any]) -> str:
        """生成完整的HTML内容"""
        
        # HTML模板
        html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaliGemma 深度架构可视化</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        {self._get_css_styles()}
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>PaliGemma 深度架构可视化</h1>
            <p>交互式模型内部结构探索工具</p>
        </header>
        
        <div class="control-panel">
            <div class="input-section">
                <div class="input-item">
                    <label>输入图像:</label>
                    <img src="{html_data['image_base64']}" alt="输入图像" class="input-image">
                </div>
                <div class="input-item">
                    <label>文本提示:</label>
                    <span class="input-text">"{html_data['metadata']['text_prompt']}"</span>
                </div>
                <div class="input-item">
                    <label>生成结果:</label>
                    <span class="output-text">"{html_data['inference_result']['output'].get('generated_text', 'N/A')}"</span>
                </div>
            </div>
            
            <div class="stats-section">
                <div class="stat-item">
                    <span class="stat-label">总节点数:</span>
                    <span class="stat-value">{html_data['stats']['totalNodes']}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">总参数量:</span>
                    <span class="stat-value">{html_data['stats']['totalParams']:,}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">执行节点:</span>
                    <span class="stat-value">{html_data['stats']['executedNodes']}</span>
                </div>
            </div>
            
            <div class="controls">
                <button id="resetView">重置视图</button>
                <button id="playAnimation">播放数据流</button>
                <button id="toggleLabels">切换标签</button>
                <button id="toggleZones">切换分区</button>
                <select id="viewMode">
                    <option value="overview">概览模式</option>
                    <option value="detailed">详细模式</option>
                    <option value="execution">执行顺序</option>
                    <option value="component">组件分组</option>
                    <option value="category">细分类别</option>
                </select>
                <select id="colorScheme">
                    <option value="component">按组件着色</option>
                    <option value="category">按类别着色</option>
                    <option value="layer">按层级着色</option>
                    <option value="parameters">按参数量着色</option>
                </select>
            </div>
        </div>
        
        <div class="visualization-container">
            <svg id="visualization"></svg>
        </div>
        
        <div class="info-panel" id="infoPanel">
            <div class="info-panel-header">
                <h3>节点信息</h3>
                <button class="info-panel-close" id="infoPanelClose">×</button>
            </div>
            <div id="nodeInfo">点击节点查看详细信息</div>
        </div>
    </div>
    
    <script>
        // 嵌入数据
        const architectureData = {json.dumps(html_data, ensure_ascii=False, indent=2)};
        
        {self._get_javascript_code()}
    </script>
</body>
</html>"""
        
        return html_template    
    def _get_css_styles(self) -> str:
        """获取CSS样式"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 100vw;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            font-weight: 300;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.8;
            font-size: 1.1em;
        }
        
        .control-panel {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 30px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .input-section {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .input-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .input-item label {
            font-weight: 600;
            color: #495057;
        }
        
        .input-image {
            max-width: 80px;
            max-height: 80px;
            border-radius: 8px;
            border: 2px solid #dee2e6;
        }
        
        .input-text, .output-text {
            background: white;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            font-family: monospace;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .output-text {
            background: #e8f5e8;
            border-color: #28a745;
        }
        
        .stats-section {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            min-width: 100px;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .stat-value {
            font-size: 1.2em;
            font-weight: 600;
            color: #495057;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-left: auto;
        }
        
        .controls button, .controls select {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 0.9em;
        }
        
        .controls button:hover {
            background: #e9ecef;
        }
        
        .visualization-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }
        
        #visualization {
            width: 100%;
            height: 100%;
            min-height: 600px;
            background: #fafafa;
            cursor: grab;
        }
        
        #visualization:active {
            cursor: grabbing;
        }
        
        .node {
            cursor: pointer;
            stroke: #fff;
            stroke-width: 2px;
            transition: all 0.3s ease;
        }
        
        .node:hover {
            stroke-width: 3px;
            filter: brightness(1.1);
        }
        
        .node.selected {
            stroke: #ff6b6b;
            stroke-width: 4px;
        }
        
        .node-label {
            font-size: 10px;
            font-weight: 500;
            text-anchor: middle;
            pointer-events: none;
            fill: #333;
        }
        
        .edge {
            stroke: #999;
            stroke-opacity: 0.6;
            fill: none;
            pointer-events: none;
        }
        
        .edge.highlighted {
            stroke: #ff6b6b;
            stroke-opacity: 1;
            stroke-width: 3px;
        }
        
        .info-panel {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            width: 300px;
            max-height: 70vh;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 20px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .info-panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }

        .info-panel h3 {
            margin: 0;
            color: #2c3e50;
        }

        .info-panel-close {
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s;
        }

        .info-panel-close:hover {
            background: #c0392b;
        }
        
        .info-item {
            margin-bottom: 12px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
            display: block;
            margin-bottom: 4px;
        }
        
        .info-value {
            font-family: monospace;
            font-size: 0.9em;
            color: #6c757d;
        }
        
        /* 组件主要分类样式 */
        .component-vision { stroke: #3498db; }
        .component-projection { stroke: #e74c3c; }
        .component-language { stroke: #2ecc71; }
        .component-other { stroke: #95a5a6; }

        /* Vision 子分类样式 */
        .category-vision_input {
            stroke: #1a5490;
            stroke-width: 3px;
            stroke-dasharray: none;
        }
        .category-vision_attention {
            stroke: #2471a3;
            stroke-width: 2px;
            stroke-dasharray: 5,5;
        }
        .category-vision_mlp {
            stroke: #5dade2;
            stroke-width: 2px;
            stroke-dasharray: 3,3;
        }
        .category-vision_norm {
            stroke: #85c1e9;
            stroke-width: 1px;
            stroke-dasharray: 1,1;
        }
        .category-vision_pooling {
            stroke: #aed6f1;
            stroke-width: 2px;
            stroke-dasharray: 8,2;
        }

        /* Projection 子分类样式 */
        .category-projection_main {
            stroke: #922b21;
            stroke-width: 4px;
            stroke-dasharray: none;
        }
        .category-projection_linear {
            stroke: #cb4335;
            stroke-width: 2px;
            stroke-dasharray: 4,4;
        }

        /* Language 子分类样式 */
        .category-language_embedding {
            stroke: #196f3d;
            stroke-width: 3px;
            stroke-dasharray: none;
        }
        .category-language_attention {
            stroke: #239b56;
            stroke-width: 2px;
            stroke-dasharray: 6,2;
        }
        .category-language_mlp {
            stroke: #58d68d;
            stroke-width: 2px;
            stroke-dasharray: 3,2;
        }
        .category-language_norm {
            stroke: #82e0aa;
            stroke-width: 1px;
            stroke-dasharray: 2,2;
        }
        .category-language_output {
            stroke: #a9dfbf;
            stroke-width: 3px;
            stroke-dasharray: 10,2;
        }

        /* 分区背景样式 */
        .zone-vision {
            fill: rgba(52, 152, 219, 0.1);
            stroke: #3498db;
            stroke-width: 2px;
            stroke-dasharray: 10,5;
        }

        .zone-projection {
            fill: rgba(231, 76, 60, 0.1);
            stroke: #e74c3c;
            stroke-width: 2px;
            stroke-dasharray: 10,5;
        }

        .zone-language {
            fill: rgba(46, 204, 113, 0.1);
            stroke: #2ecc71;
            stroke-width: 2px;
            stroke-dasharray: 10,5;
        }

        .zone-other {
            fill: rgba(149, 165, 166, 0.1);
            stroke: #95a5a6;
            stroke-width: 2px;
            stroke-dasharray: 10,5;
        }
        
        .legend {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            max-height: 70vh;
            overflow-y: auto;
            min-width: 250px;
        }

        .legend-section {
            margin-bottom: 15px;
        }

        .legend-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 8px;
            color: #2c3e50;
            border-bottom: 1px solid #ecf0f1;
            padding-bottom: 4px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
            font-size: 12px;
        }

        .legend-color {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            border: 1px solid #bdc3c7;
        }

        .legend-shape {
            width: 14px;
            height: 14px;
            border: 2px solid #34495e;
        }

        .legend-shape.circle { border-radius: 50%; }
        .legend-shape.square { border-radius: 2px; }
        .legend-shape.diamond {
            transform: rotate(45deg);
            border-radius: 2px;
        }
        .legend-shape.triangle {
            width: 0;
            height: 0;
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            border-bottom: 12px solid #34495e;
            border-top: none;
        }

        .legend-toggle {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            z-index: 1001;
        }

        .legend-toggle:hover {
            background: #2980b9;
        }

        .legend.collapsed {
            display: none;
        }
        
        @media (max-width: 768px) {
            .control-panel {
                flex-direction: column;
                align-items: stretch;
            }
            
            .input-section, .stats-section, .controls {
                justify-content: center;
            }
            
            .info-panel {
                position: fixed;
                bottom: 0;
                right: 0;
                left: 0;
                top: auto;
                transform: none;
                width: auto;
                max-height: 50vh;
                border-radius: 12px 12px 0 0;
            }
        }
        """
    
    def _get_javascript_code(self) -> str:
        """获取JavaScript代码"""
        return """
        // 全局变量
        let svg, g, simulation, nodes, edges;
        let selectedNode = null;
        let showLabels = true;
        let showZones = false;  // 默认不显示分区
        let currentViewMode = 'overview';
        let currentColorScheme = 'component';
        
        // 初始化可视化
        function initVisualization() {
            const container = d3.select('.visualization-container');
            const containerRect = container.node().getBoundingClientRect();
            
            svg = d3.select('#visualization')
                .attr('width', containerRect.width)
                .attr('height', containerRect.height);
            
            // 添加缩放和平移
            const zoom = d3.zoom()
                .scaleExtent([0.1, 3])
                .on('zoom', (event) => {
                    g.attr('transform', event.transform);
                });
            
            svg.call(zoom);
            
            // 创建主绘图组
            g = svg.append('g');
            
            // 创建力导向布局
            simulation = d3.forceSimulation()
                .force('link', d3.forceLink().id(d => d.id).distance(100))
                .force('charge', d3.forceManyBody().strength(-300))
                .force('center', d3.forceCenter(containerRect.width / 2, containerRect.height / 2))
                .force('collision', d3.forceCollide().radius(d => d.size + 5));
            
            // 渲染数据
            renderVisualization();
            
            // 添加图例
            createLegend();
        }
        
        function renderVisualization() {
            const data = architectureData;
            nodes = data.nodes;
            edges = data.edges;
            
            // 渲染边
            const edgeSelection = g.selectAll('.edge')
                .data(edges)
                .enter()
                .append('line')
                .attr('class', 'edge')
                .attr('stroke-width', d => d.width);
            
            // 渲染分区背景
            renderZones();

            // 渲染节点
            const nodeSelection = g.selectAll('.node')
                .data(nodes)
                .enter()
                .append('g')
                .attr('class', d => `node-group component-${d.component} category-${d.category}`)
                .on('click', handleNodeClick)
                .on('mouseover', handleNodeMouseOver)
                .on('mouseout', handleNodeMouseOut);

            // 根据形状类型渲染不同的节点
            nodeSelection.each(function(d) {
                const group = d3.select(this);
                renderNodeShape(group, d);
            });
            
            // 添加节点标签
            const labelSelection = g.selectAll('.node-label')
                .data(nodes)
                .enter()
                .append('text')
                .attr('class', 'node-label')
                .text(d => d.name)
                .style('display', showLabels ? 'block' : 'none');
            
            // 启动仿真
            simulation
                .nodes(nodes)
                .on('tick', () => {
                    edgeSelection
                        .attr('x1', d => d.source.x)
                        .attr('y1', d => d.source.y)
                        .attr('x2', d => d.target.x)
                        .attr('y2', d => d.target.y);
                    
                    nodeSelection
                        .attr('transform', d => `translate(${d.x},${d.y})`);

                    labelSelection
                        .attr('x', d => d.x)
                        .attr('y', d => d.y + d.size + 12);
                });
            
            simulation.force('link').links(edges);
        }

        function renderZones() {
            // 默认不显示分区，用户可以通过按钮控制
            if (!showZones) return;

            // 计算每个组件的节点分布范围
            const componentBounds = {};
            const padding = 50;

            nodes.forEach(node => {
                if (!componentBounds[node.component]) {
                    componentBounds[node.component] = {
                        minX: node.x, maxX: node.x,
                        minY: node.y, maxY: node.y,
                        nodes: []
                    };
                }
                const bounds = componentBounds[node.component];
                bounds.minX = Math.min(bounds.minX, node.x);
                bounds.maxX = Math.max(bounds.maxX, node.x);
                bounds.minY = Math.min(bounds.minY, node.y);
                bounds.maxY = Math.max(bounds.maxY, node.y);
                bounds.nodes.push(node);
            });

            // 渲染分区背景
            Object.keys(componentBounds).forEach(component => {
                const bounds = componentBounds[component];
                if (bounds.nodes.length > 1) {  // 只为有多个节点的组件绘制分区
                    g.append('rect')
                        .attr('class', `zone zone-${component}`)
                        .attr('x', bounds.minX - padding)
                        .attr('y', bounds.minY - padding)
                        .attr('width', bounds.maxX - bounds.minX + 2 * padding)
                        .attr('height', bounds.maxY - bounds.minY + 2 * padding)
                        .attr('rx', 10)
                        .style('pointer-events', 'none');
                }
            });
        }

        function renderNodeShape(group, data) {
            const shape = data.shape || 'circle';
            const size = data.size;
            const color = data.color;
            const strokeColor = data.strokeColor || '#fff';

            switch(shape) {
                case 'circle':
                    group.append('circle')
                        .attr('class', 'node')
                        .attr('r', size)
                        .attr('fill', color)
                        .attr('stroke', strokeColor);
                    break;

                case 'square':
                    group.append('rect')
                        .attr('class', 'node')
                        .attr('width', size * 2)
                        .attr('height', size * 2)
                        .attr('x', -size)
                        .attr('y', -size)
                        .attr('fill', color)
                        .attr('stroke', strokeColor);
                    break;

                case 'diamond':
                    group.append('rect')
                        .attr('class', 'node')
                        .attr('width', size * 1.4)
                        .attr('height', size * 1.4)
                        .attr('x', -size * 0.7)
                        .attr('y', -size * 0.7)
                        .attr('transform', 'rotate(45)')
                        .attr('fill', color)
                        .attr('stroke', strokeColor);
                    break;

                case 'triangle':
                    const points = [
                        [0, -size],
                        [-size * 0.866, size * 0.5],
                        [size * 0.866, size * 0.5]
                    ].map(p => p.join(',')).join(' ');

                    group.append('polygon')
                        .attr('class', 'node')
                        .attr('points', points)
                        .attr('fill', color)
                        .attr('stroke', strokeColor);
                    break;

                case 'hexagon':
                    const hexPoints = [];
                    for (let i = 0; i < 6; i++) {
                        const angle = (i * 60) * Math.PI / 180;
                        hexPoints.push([
                            size * Math.cos(angle),
                            size * Math.sin(angle)
                        ]);
                    }

                    group.append('polygon')
                        .attr('class', 'node')
                        .attr('points', hexPoints.map(p => p.join(',')).join(' '))
                        .attr('fill', color)
                        .attr('stroke', strokeColor);
                    break;

                case 'star':
                    const starPoints = [];
                    for (let i = 0; i < 10; i++) {
                        const angle = (i * 36) * Math.PI / 180;
                        const radius = i % 2 === 0 ? size : size * 0.5;
                        starPoints.push([
                            radius * Math.cos(angle - Math.PI / 2),
                            radius * Math.sin(angle - Math.PI / 2)
                        ]);
                    }

                    group.append('polygon')
                        .attr('class', 'node')
                        .attr('points', starPoints.map(p => p.join(',')).join(' '))
                        .attr('fill', color)
                        .attr('stroke', strokeColor);
                    break;

                default:
                    // 默认圆形
                    group.append('circle')
                        .attr('class', 'node')
                        .attr('r', size)
                        .attr('fill', color)
                        .attr('stroke', strokeColor);
            }
        }
        
        function handleNodeClick(event, d) {
            // 取消之前的选择
            g.selectAll('.node-group').classed('selected', false);
            g.selectAll('.node').classed('selected', false);
            g.selectAll('.edge').classed('highlighted', false);

            // 选择当前节点组
            d3.select(event.currentTarget).classed('selected', true);
            d3.select(event.currentTarget).select('.node').classed('selected', true);
            selectedNode = d;

            // 高亮相关边
            g.selectAll('.edge')
                .classed('highlighted', edge =>
                    edge.source.id === d.id || edge.target.id === d.id);

            // 显示节点信息
            showNodeInfo(d);
        }
        
        function handleNodeMouseOver(event, d) {
            // 显示简单的tooltip
            const tooltip = d3.select('body').append('div')
                .attr('class', 'tooltip')
                .style('position', 'absolute')
                .style('background', 'rgba(0,0,0,0.8)')
                .style('color', 'white')
                .style('padding', '8px')
                .style('border-radius', '4px')
                .style('font-size', '12px')
                .style('pointer-events', 'none')
                .style('z-index', '1001')
                .html(`
                    <strong>${d.name}</strong><br>
                    类型: ${d.type}<br>
                    参数: ${d.parameters.total.toLocaleString()}
                `);
            
            tooltip
                .style('left', (event.pageX + 10) + 'px')
                .style('top', (event.pageY - 10) + 'px');
        }
        
        function handleNodeMouseOut() {
            d3.selectAll('.tooltip').remove();
        }
        
        function showNodeInfo(node) {
            const infoPanel = d3.select('#infoPanel');
            const nodeInfo = d3.select('#nodeInfo');
            
            let html = `
                <div class="info-item">
                    <span class="info-label">节点名称:</span>
                    <span class="info-value">${node.fullName}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">模块类型:</span>
                    <span class="info-value">${node.type}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">组件:</span>
                    <span class="info-value">${node.component}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">层索引:</span>
                    <span class="info-value">${node.layer >= 0 ? node.layer : 'N/A'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">执行顺序:</span>
                    <span class="info-value">${node.executionOrder >= 0 ? node.executionOrder : 'N/A'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">总参数:</span>
                    <span class="info-value">${node.parameters.total.toLocaleString()}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">可训练参数:</span>
                    <span class="info-value">${node.parameters.trainable.toLocaleString()}</span>
                </div>
            `;
            
            if (node.inputShape && node.inputShape.length > 0) {
                html += `
                    <div class="info-item">
                        <span class="info-label">输入形状:</span>
                        <span class="info-value">${JSON.stringify(node.inputShape)}</span>
                    </div>
                `;
            }
            
            if (node.outputShape && node.outputShape.length > 0) {
                html += `
                    <div class="info-item">
                        <span class="info-label">输出形状:</span>
                        <span class="info-value">${JSON.stringify(node.outputShape)}</span>
                    </div>
                `;
            }
            
            if (node.dataStats && Object.keys(node.dataStats).length > 0) {
                html += `
                    <div class="info-item">
                        <span class="info-label">数据统计:</span>
                        <span class="info-value">${JSON.stringify(node.dataStats, null, 2)}</span>
                    </div>
                `;
            }
            
            nodeInfo.html(html);
            infoPanel.style('display', 'block');
        }
        
        function createLegend() {
            // 创建图例切换按钮
            const toggleButton = d3.select('body').append('button')
                .attr('class', 'legend-toggle')
                .text('图例')
                .on('click', function() {
                    const legend = d3.select('.legend');
                    const isCollapsed = legend.classed('collapsed');
                    legend.classed('collapsed', !isCollapsed);
                    d3.select(this).text(isCollapsed ? '图例' : '隐藏');
                });

            const legend = d3.select('body').append('div')
                .attr('class', 'legend');

            // 主要组件分类
            const mainSection = legend.append('div').attr('class', 'legend-section');
            mainSection.append('div').attr('class', 'legend-title').text('主要组件');

            const mainLegendData = [
                { color: '#3498db', label: 'Vision Encoder', shape: 'circle' },
                { color: '#e74c3c', label: 'Projection Layer', shape: 'square' },
                { color: '#2ecc71', label: 'Language Model', shape: 'circle' },
                { color: '#95a5a6', label: 'Other Components', shape: 'circle' }
            ];

            mainSection.selectAll('.legend-item')
                .data(mainLegendData)
                .enter()
                .append('div')
                .attr('class', 'legend-item')
                .html(d => `
                    <div class="legend-color" style="background-color: ${d.color}"></div>
                    <span>${d.label}</span>
                `);

            // Vision 子分类
            const visionSection = legend.append('div').attr('class', 'legend-section');
            visionSection.append('div').attr('class', 'legend-title').text('Vision Encoder 细分');

            const visionLegendData = [
                { color: '#1a5490', label: 'Input Layer (Patch Embedding)', shape: 'circle', pattern: 'solid' },
                { color: '#2471a3', label: 'Attention Layers', shape: 'diamond', pattern: 'dashed' },
                { color: '#5dade2', label: 'MLP Layers', shape: 'square', pattern: 'dotted' },
                { color: '#85c1e9', label: 'Normalization', shape: 'triangle', pattern: 'dotted' },
                { color: '#aed6f1', label: 'Pooling', shape: 'hexagon', pattern: 'dashed' }
            ];

            visionSection.selectAll('.legend-item')
                .data(visionLegendData)
                .enter()
                .append('div')
                .attr('class', 'legend-item')
                .html(d => `
                    <div class="legend-color" style="background-color: ${d.color}"></div>
                    <span>${d.label}</span>
                `);

            // Language 子分类
            const languageSection = legend.append('div').attr('class', 'legend-section');
            languageSection.append('div').attr('class', 'legend-title').text('Language Model 细分');

            const languageLegendData = [
                { color: '#196f3d', label: 'Embedding Layers', shape: 'pentagon', pattern: 'solid' },
                { color: '#239b56', label: 'Attention Layers', shape: 'star', pattern: 'dashed' },
                { color: '#58d68d', label: 'MLP/Feed Forward', shape: 'octagon', pattern: 'dotted' },
                { color: '#82e0aa', label: 'Normalization', shape: 'triangle', pattern: 'dotted' },
                { color: '#a9dfbf', label: 'Output Head', shape: 'diamond', pattern: 'solid' }
            ];

            languageSection.selectAll('.legend-item')
                .data(languageLegendData)
                .enter()
                .append('div')
                .attr('class', 'legend-item')
                .html(d => `
                    <div class="legend-color" style="background-color: ${d.color}"></div>
                    <span>${d.label}</span>
                `);

            // 形状说明
            const shapeSection = legend.append('div').attr('class', 'legend-section');
            shapeSection.append('div').attr('class', 'legend-title').text('节点形状说明');

            const shapeLegendData = [
                { shape: 'circle', label: '基础层/其他' },
                { shape: 'square', label: 'MLP/线性层' },
                { shape: 'diamond', label: '注意力层' },
                { shape: 'triangle', label: '归一化层' },
                { shape: 'star', label: '特殊注意力' },
                { shape: 'hexagon', label: '池化层' }
            ];

            shapeSection.selectAll('.legend-item')
                .data(shapeLegendData)
                .enter()
                .append('div')
                .attr('class', 'legend-item')
                .html(d => `
                    <div class="legend-shape ${d.shape}"></div>
                    <span>${d.label}</span>
                `);
        }
        
        // 控制按钮事件
        document.getElementById('resetView').addEventListener('click', () => {
            svg.transition().duration(750).call(
                d3.zoom().transform,
                d3.zoomIdentity
            );
        });
        
        document.getElementById('toggleLabels').addEventListener('click', () => {
            showLabels = !showLabels;
            g.selectAll('.node-label')
                .style('display', showLabels ? 'block' : 'none');
        });
        
        document.getElementById('playAnimation').addEventListener('click', () => {
            playDataFlowAnimation();
        });
        
        document.getElementById('viewMode').addEventListener('change', (e) => {
            currentViewMode = e.target.value;
            updateViewMode();
        });

        document.getElementById('toggleZones').addEventListener('click', () => {
            showZones = !showZones;
            g.selectAll('.zone').style('display', showZones ? 'block' : 'none');
            document.getElementById('toggleZones').textContent = showZones ? '隐藏分区' : '显示分区';
        });

        document.getElementById('colorScheme').addEventListener('change', (e) => {
            currentColorScheme = e.target.value;
            updateColorScheme();
        });

        // 信息面板关闭按钮
        document.getElementById('infoPanelClose').addEventListener('click', () => {
            const infoPanel = document.getElementById('infoPanel');
            infoPanel.style.display = 'none';
            // 取消节点选择状态
            g.selectAll('.node-group').classed('selected', false);
            g.selectAll('.node').classed('selected', false);
            g.selectAll('.edge').classed('highlighted', false);
            selectedNode = null;
        });
        
        function playDataFlowAnimation() {
            const executedNodes = nodes
                .filter(n => n.executionOrder >= 0)
                .sort((a, b) => a.executionOrder - b.executionOrder);

            // 重置所有节点和边
            g.selectAll('.node-group').style('opacity', 0.3);
            g.selectAll('.node').style('opacity', 0.3);
            g.selectAll('.edge').style('opacity', 0.1);

            // 逐个高亮节点
            executedNodes.forEach((node, i) => {
                setTimeout(() => {
                    // 高亮节点组
                    const nodeGroup = g.selectAll('.node-group')
                        .filter(d => d.id === node.id);

                    nodeGroup
                        .transition()
                        .duration(300)
                        .style('opacity', 1);

                    // 高亮节点并添加脉冲效果
                    const nodeElement = nodeGroup.select('.node');
                    const originalSize = node.size;

                    nodeElement
                        .transition()
                        .duration(300)
                        .style('opacity', 1)
                        .attr('r', originalSize * 1.5)  // 对圆形节点
                        .transition()
                        .duration(300)
                        .attr('r', originalSize);

                    // 对于非圆形节点，调整transform scale
                    if (node.shape !== 'circle') {
                        nodeGroup
                            .transition()
                            .duration(300)
                            .attr('transform', d => `translate(${d.x},${d.y}) scale(1.3)`)
                            .transition()
                            .duration(300)
                            .attr('transform', d => `translate(${d.x},${d.y}) scale(1)`);
                    }

                    // 高亮相关边
                    g.selectAll('.edge')
                        .filter(edge => edge.source.id === node.id)
                        .transition()
                        .duration(300)
                        .style('opacity', 1);

                }, i * 200);
            });

            // 动画结束后恢复
            setTimeout(() => {
                g.selectAll('.node-group').transition().duration(500).style('opacity', 1);
                g.selectAll('.node').transition().duration(500).style('opacity', 1);
                g.selectAll('.edge').transition().duration(500).style('opacity', 0.6);
            }, executedNodes.length * 200 + 1000);
        }
        
        function updateViewMode() {
            // 根据视图模式调整布局
            switch(currentViewMode) {
                case 'overview':
                    // 概览模式：按组件分组
                    updateForceLayout('component');
                    break;
                case 'detailed':
                    // 详细模式：显示所有连接
                    updateForceLayout('detailed');
                    break;
                case 'execution':
                    // 执行顺序模式：按执行顺序排列
                    updateForceLayout('execution');
                    break;
                case 'component':
                    // 组件分组模式：严格按组件分组
                    updateForceLayout('component_strict');
                    break;
                case 'category':
                    // 细分类别模式：按详细分类分组
                    updateForceLayout('category');
                    break;
            }
        }

        function updateColorScheme() {
            // 根据颜色方案更新节点颜色
            g.selectAll('.node').each(function(d) {
                const node = d3.select(this);
                let newColor;

                switch(currentColorScheme) {
                    case 'component':
                        newColor = d.color; // 使用原始组件颜色
                        break;
                    case 'category':
                        newColor = getCategoryColor(d.category);
                        break;
                    case 'layer':
                        newColor = getLayerColor(d.layer);
                        break;
                    case 'parameters':
                        newColor = getParameterColor(d.parameters.total);
                        break;
                    default:
                        newColor = d.color;
                }

                node.transition().duration(500).attr('fill', newColor);
            });
        }

        function getCategoryColor(category) {
            const categoryColors = {
                'vision_input': '#1a5490',
                'vision_attention': '#2471a3',
                'vision_mlp': '#5dade2',
                'vision_norm': '#85c1e9',
                'vision_pooling': '#aed6f1',
                'vision_other': '#d6eaf8',
                'projection_main': '#922b21',
                'projection_linear': '#cb4335',
                'projection_other': '#f1948a',
                'language_embedding': '#196f3d',
                'language_attention': '#239b56',
                'language_mlp': '#58d68d',
                'language_norm': '#82e0aa',
                'language_output': '#a9dfbf',
                'language_other': '#d5f4e6',
                'other': '#566573'
            };
            return categoryColors[category] || '#95a5a6';
        }

        function getLayerColor(layer) {
            if (layer < 0) return '#95a5a6';
            const hue = (layer * 30) % 360;
            return `hsl(${hue}, 70%, 60%)`;
        }

        function getParameterColor(paramCount) {
            if (paramCount === 0) return '#ecf0f1';
            const intensity = Math.min(1, Math.log10(paramCount) / 8);
            const red = Math.floor(255 * intensity);
            const blue = Math.floor(255 * (1 - intensity));
            return `rgb(${red}, 100, ${blue})`;
        }
        
        function updateForceLayout(mode) {
            // 根据模式调整力的参数
            switch(mode) {
                case 'execution':
                    simulation
                        .force('x', d3.forceX(d => d.executionOrder * 50 + 100).strength(0.5))
                        .force('y', d3.forceY(300).strength(0.1));
                    break;

                case 'component':
                case 'component_strict':
                    const componentX = { vision: 200, projection: 500, language: 800, other: 1100 };
                    const strength = mode === 'component_strict' ? 0.8 : 0.5;
                    simulation
                        .force('x', d3.forceX(d => componentX[d.component] || 1100).strength(strength))
                        .force('y', d3.forceY(d => d.layer * 50 + 200).strength(0.3));
                    break;

                case 'category':
                    // 按详细分类分组
                    const categoryPositions = {
                        // Vision 分类
                        'vision_input': { x: 150, y: 150 },
                        'vision_attention': { x: 150, y: 250 },
                        'vision_mlp': { x: 150, y: 350 },
                        'vision_norm': { x: 150, y: 450 },
                        'vision_pooling': { x: 150, y: 550 },
                        'vision_other': { x: 150, y: 650 },

                        // Projection 分类
                        'projection_main': { x: 450, y: 300 },
                        'projection_linear': { x: 450, y: 400 },
                        'projection_other': { x: 450, y: 500 },

                        // Language 分类
                        'language_embedding': { x: 750, y: 150 },
                        'language_attention': { x: 750, y: 250 },
                        'language_mlp': { x: 750, y: 350 },
                        'language_norm': { x: 750, y: 450 },
                        'language_output': { x: 750, y: 550 },
                        'language_other': { x: 750, y: 650 },

                        // 其他
                        'other': { x: 1050, y: 400 }
                    };

                    simulation
                        .force('x', d3.forceX(d => {
                            const pos = categoryPositions[d.category];
                            return pos ? pos.x : 1050;
                        }).strength(0.7))
                        .force('y', d3.forceY(d => {
                            const pos = categoryPositions[d.category];
                            return pos ? pos.y : 400;
                        }).strength(0.7));
                    break;

                case 'detailed':
                default:
                    simulation
                        .force('x', null)
                        .force('y', null);
                    break;
            }

            simulation.alpha(0.3).restart();
        }
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            const container = d3.select('.visualization-container');
            const containerRect = container.node().getBoundingClientRect();
            
            svg
                .attr('width', containerRect.width)
                .attr('height', containerRect.height);
            
            simulation
                .force('center', d3.forceCenter(containerRect.width / 2, containerRect.height / 2))
                .alpha(0.3)
                .restart();
        });
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initVisualization();
        });
        """

def main():
    """主函数 - 用于测试"""
    # 读取分析结果
    analysis_path = "/home/<USER>/dataset/X/X00_Architecture/architecture_analysis.json"
    if not os.path.exists(analysis_path):
        print(f"分析结果文件不存在: {analysis_path}")
        return
    
    with open(analysis_path, 'r', encoding='utf-8') as f:
        analysis_result = json.load(f)
    
    # 生成HTML
    generator = InteractiveHTMLGenerator()
    output_path = "/home/<USER>/dataset/X/X00_Architecture/paligemma_architecture_visualization.html"
    generator.generate_html(analysis_result, output_path)
    
    print(f"HTML可视化已生成: {output_path}")

if __name__ == "__main__":
    main()