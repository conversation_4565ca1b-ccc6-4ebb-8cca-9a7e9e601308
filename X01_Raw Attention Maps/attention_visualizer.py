#!/usr/bin/env python3
"""
PaliGemma 注意力图可视化器
提取并可视化每一层对图片patch和文本token的注意力权重
"""

import os
import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import json
from typing import Dict, List, Any, Tuple, Optional
from transformers import AutoProcessor, PaliGemmaForConditionalGeneration
from PIL import Image
import warnings
warnings.filterwarnings('ignore')

class AttentionVisualizer:
    """PaliGemma注意力可视化器"""
    
    def __init__(self, model_path: str, device: str = "cuda:0"):
        """初始化可视化器"""
        self.model_path = model_path
        self.device = device
        self.model = None
        self.processor = None
        
        # 注意力数据存储
        self.attention_weights = {}  # 存储所有层的注意力权重
        self.hooks = []  # 注册的hooks
        self.layer_names = []  # 层名称列表
        
        # 输入信息
        self.image_patches = None
        self.text_tokens = None
        self.input_ids = None
        self.pixel_values = None
        
        print("正在初始化PaliGemma注意力可视化器...")
        self._load_model()
        
    def _load_model(self):
        """加载模型和处理器"""
        try:
            print(f"正在加载模型: {self.model_path}")
            self.model = PaliGemmaForConditionalGeneration.from_pretrained(
                self.model_path,
                torch_dtype=torch.float16,  # 改为float16避免BFloat16问题
                device_map=self.device,
                trust_remote_code=True
            )
            
            self.processor = AutoProcessor.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )
            
            self.model.eval()
            print("模型加载完成!")
            
        except Exception as e:
            print(f"模型加载失败: {e}")
            raise
    
    def extract_attention_maps(self, image_path: str, text_prompt: str) -> Dict[str, Any]:
        """提取注意力图"""
        print(f"正在处理图片: {image_path}")
        print(f"文本提示: {text_prompt}")
        
        # 准备输入
        image = Image.open(image_path).convert("RGB")
        inputs = self.processor(images=image, text=text_prompt, return_tensors="pt").to(self.device)
        
        # 存储输入信息
        self.input_ids = inputs['input_ids']
        self.pixel_values = inputs['pixel_values']
        
        # 解码文本tokens
        self.text_tokens = self.processor.tokenizer.convert_ids_to_tokens(
            self.input_ids[0].cpu().numpy()
        )
        
        # 计算图片patch数量 (PaliGemma使用SigLIP vision encoder)
        # 通常是 224x224 图片被分成 14x14 = 196 个patches
        vision_config = self.model.config.vision_config
        image_size = vision_config.image_size
        patch_size = vision_config.patch_size
        num_patches = (image_size // patch_size) ** 2
        self.image_patches = num_patches
        
        print(f"图片patches数量: {num_patches}")
        print(f"文本tokens数量: {len(self.text_tokens)}")
        print(f"文本tokens: {self.text_tokens}")
        
        # 注册attention hooks
        self._register_attention_hooks()
        
        try:
            # 运行推理
            print("正在运行推理...")
            with torch.no_grad():
                outputs = self.model(**inputs)
            
            print("推理完成，正在处理注意力数据...")
            
            # 处理收集到的注意力权重
            processed_attention = self._process_attention_weights()
            
            return {
                'attention_weights': processed_attention,
                'text_tokens': self.text_tokens,
                'num_image_patches': num_patches,
                'image_path': image_path,
                'text_prompt': text_prompt,
                'model_info': {
                    'num_layers': len(processed_attention),
                    'layer_names': list(processed_attention.keys())
                }
            }
            
        except Exception as e:
            print(f"推理过程中出错: {e}")
            raise
        finally:
            self._remove_hooks()
    
    def _register_attention_hooks(self):
        """注册注意力权重捕获hooks"""
        print("正在注册attention hooks...")
        
        # 遍历所有模块，找到attention层
        for name, module in self.model.named_modules():
            # 查找多头注意力层
            if hasattr(module, 'self_attn') or 'attention' in name.lower():
                if hasattr(module, 'self_attn'):
                    # Transformer层的self attention
                    hook = self._create_attention_hook(f"{name}.self_attn")
                    handle = module.self_attn.register_forward_hook(hook)
                    self.hooks.append(handle)
                    self.layer_names.append(f"{name}.self_attn")
                elif isinstance(module, nn.MultiheadAttention):
                    # 直接的MultiheadAttention层
                    hook = self._create_attention_hook(name)
                    handle = module.register_forward_hook(hook)
                    self.hooks.append(handle)
                    self.layer_names.append(name)
        
        print(f"已注册 {len(self.hooks)} 个attention hooks")
        print(f"监控的层: {self.layer_names}")
    
    def _create_attention_hook(self, layer_name: str):
        """创建注意力权重捕获hook"""
        def hook_fn(module, input, output):
            try:
                # 不同的attention层可能有不同的输出格式
                if isinstance(output, tuple) and len(output) >= 2:
                    # 通常格式: (output, attention_weights)
                    attention_weights = output[1]
                elif hasattr(output, 'attentions'):
                    attention_weights = output.attentions
                else:
                    # 尝试从模块属性中获取
                    if hasattr(module, 'attention_weights'):
                        attention_weights = module.attention_weights
                    else:
                        return
                
                if attention_weights is not None:
                    # 存储注意力权重，转换为float32避免BFloat16问题
                    attention_float = attention_weights.detach().float().cpu()
                    self.attention_weights[layer_name] = attention_float
                    print(f"捕获到 {layer_name} 的注意力权重: {attention_weights.shape}")
                    
            except Exception as e:
                print(f"Hook错误 {layer_name}: {e}")
        
        return hook_fn
    
    def _process_attention_weights(self) -> Dict[str, Any]:
        """处理收集到的注意力权重"""
        processed = {}
        
        for layer_name, attention in self.attention_weights.items():
            try:
                # attention shape: [batch_size, num_heads, seq_len, seq_len]
                if len(attention.shape) == 4:
                    batch_size, num_heads, seq_len, _ = attention.shape
                    
                    # 平均所有头的注意力
                    avg_attention = attention.mean(dim=1)  # [batch_size, seq_len, seq_len]
                    
                    # 取第一个batch
                    attention_matrix = avg_attention[0].numpy()  # [seq_len, seq_len]
                    
                    # 分析注意力模式
                    analysis = self._analyze_attention_pattern(attention_matrix, layer_name)
                    
                    processed[layer_name] = {
                        'attention_matrix': attention_matrix,
                        'shape': attention_matrix.shape,
                        'num_heads': num_heads,
                        'analysis': analysis
                    }
                    
                else:
                    print(f"警告: {layer_name} 的注意力权重形状不符合预期: {attention.shape}")
                    
            except Exception as e:
                print(f"处理 {layer_name} 注意力权重时出错: {e}")
        
        return processed
    
    def _analyze_attention_pattern(self, attention_matrix: np.ndarray, layer_name: str) -> Dict[str, Any]:
        """分析注意力模式"""
        seq_len = attention_matrix.shape[0]
        
        # 估计图片patches和文本tokens的分界
        # PaliGemma中，通常图片patches在前，文本tokens在后
        num_image_patches = self.image_patches
        
        if seq_len > num_image_patches:
            # 分离图片和文本部分
            image_to_image = attention_matrix[:num_image_patches, :num_image_patches]
            image_to_text = attention_matrix[:num_image_patches, num_image_patches:]
            text_to_image = attention_matrix[num_image_patches:, :num_image_patches]
            text_to_text = attention_matrix[num_image_patches:, num_image_patches:]
            
            analysis = {
                'total_sequence_length': seq_len,
                'num_image_patches': num_image_patches,
                'num_text_tokens': seq_len - num_image_patches,
                'attention_patterns': {
                    'image_to_image': {
                        'shape': image_to_image.shape,
                        'mean_attention': float(image_to_image.mean()),
                        'max_attention': float(image_to_image.max()),
                        'std_attention': float(image_to_image.std())
                    },
                    'image_to_text': {
                        'shape': image_to_text.shape,
                        'mean_attention': float(image_to_text.mean()),
                        'max_attention': float(image_to_text.max()),
                        'std_attention': float(image_to_text.std())
                    },
                    'text_to_image': {
                        'shape': text_to_image.shape,
                        'mean_attention': float(text_to_image.mean()),
                        'max_attention': float(text_to_image.max()),
                        'std_attention': float(text_to_image.std())
                    },
                    'text_to_text': {
                        'shape': text_to_text.shape,
                        'mean_attention': float(text_to_text.mean()),
                        'max_attention': float(text_to_text.max()),
                        'std_attention': float(text_to_text.std())
                    }
                }
            }
        else:
            # 只有图片patches
            analysis = {
                'total_sequence_length': seq_len,
                'num_image_patches': seq_len,
                'num_text_tokens': 0,
                'attention_patterns': {
                    'image_only': {
                        'shape': attention_matrix.shape,
                        'mean_attention': float(attention_matrix.mean()),
                        'max_attention': float(attention_matrix.max()),
                        'std_attention': float(attention_matrix.std())
                    }
                }
            }
        
        return analysis
    
    def _remove_hooks(self):
        """移除所有hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
        self.attention_weights.clear()

    def visualize_attention_maps(self, attention_data: Dict[str, Any], output_dir: str = "attention_visualizations"):
        """可视化注意力图"""
        os.makedirs(output_dir, exist_ok=True)

        attention_weights = attention_data['attention_weights']
        text_tokens = attention_data['text_tokens']
        num_image_patches = attention_data['num_image_patches']

        print(f"正在生成 {len(attention_weights)} 层的注意力可视化...")

        # 为每一层生成可视化
        for layer_idx, (layer_name, layer_data) in enumerate(attention_weights.items()):
            print(f"处理第 {layer_idx+1} 层: {layer_name}")

            attention_matrix = layer_data['attention_matrix']
            analysis = layer_data['analysis']

            # 创建综合可视化图
            self._create_layer_visualization(
                attention_matrix, layer_name, layer_idx,
                text_tokens, num_image_patches, analysis, output_dir
            )

        # 生成汇总报告
        self._generate_summary_report(attention_data, output_dir)

        print(f"所有可视化已保存到: {output_dir}")

    def _create_layer_visualization(self, attention_matrix: np.ndarray, layer_name: str,
                                  layer_idx: int, text_tokens: List[str],
                                  num_image_patches: int, analysis: Dict, output_dir: str):
        """为单个层创建可视化"""

        # 设置图形大小
        fig = plt.figure(figsize=(20, 15))

        # 主标题
        fig.suptitle(f'Layer {layer_idx+1}: {layer_name} - Attention Patterns',
                    fontsize=16, fontweight='bold')

        # 1. 完整注意力矩阵热图
        ax1 = plt.subplot(2, 3, 1)
        sns.heatmap(attention_matrix, cmap='Blues', cbar=True, square=True)
        ax1.set_title('Complete Attention Matrix')
        ax1.set_xlabel('Key Position')
        ax1.set_ylabel('Query Position')

        # 添加分界线标记图片和文本区域
        if num_image_patches < attention_matrix.shape[0]:
            ax1.axhline(y=num_image_patches, color='red', linestyle='--', alpha=0.7)
            ax1.axvline(x=num_image_patches, color='red', linestyle='--', alpha=0.7)
            ax1.text(num_image_patches/2, -5, 'Image Patches', ha='center', color='blue')
            ax1.text(num_image_patches + (attention_matrix.shape[1]-num_image_patches)/2, -5,
                    'Text Tokens', ha='center', color='green')

        # 2. 图片patch间的注意力
        if num_image_patches < attention_matrix.shape[0]:
            ax2 = plt.subplot(2, 3, 2)
            image_attention = attention_matrix[:num_image_patches, :num_image_patches]
            sns.heatmap(image_attention, cmap='Reds', cbar=True, square=True)
            ax2.set_title('Image Patch to Image Patch Attention')
            ax2.set_xlabel('Image Patch (Key)')
            ax2.set_ylabel('Image Patch (Query)')

        # 3. 文本对图片的注意力
        if num_image_patches < attention_matrix.shape[0]:
            ax3 = plt.subplot(2, 3, 3)
            text_to_image = attention_matrix[num_image_patches:, :num_image_patches]
            sns.heatmap(text_to_image, cmap='Greens', cbar=True)
            ax3.set_title('Text to Image Attention')
            ax3.set_xlabel('Image Patch (Key)')
            ax3.set_ylabel('Text Token (Query)')

            # 添加文本token标签
            if len(text_tokens) > num_image_patches:
                text_labels = text_tokens[num_image_patches:]
                if len(text_labels) <= 20:  # 只在token数量不太多时显示标签
                    ax3.set_yticklabels(text_labels, rotation=0, fontsize=8)

        # 4. 图片对文本的注意力
        if num_image_patches < attention_matrix.shape[0]:
            ax4 = plt.subplot(2, 3, 4)
            image_to_text = attention_matrix[:num_image_patches, num_image_patches:]
            sns.heatmap(image_to_text, cmap='Purples', cbar=True)
            ax4.set_title('Image to Text Attention')
            ax4.set_ylabel('Image Patch (Query)')
            ax4.set_xlabel('Text Token (Key)')

            # 添加文本token标签
            if len(text_tokens) > num_image_patches:
                text_labels = text_tokens[num_image_patches:]
                if len(text_labels) <= 20:
                    ax4.set_xticklabels(text_labels, rotation=45, fontsize=8)

        # 5. 文本间的注意力
        if num_image_patches < attention_matrix.shape[0]:
            ax5 = plt.subplot(2, 3, 5)
            text_attention = attention_matrix[num_image_patches:, num_image_patches:]
            sns.heatmap(text_attention, cmap='Oranges', cbar=True, square=True)
            ax5.set_title('Text to Text Attention')
            ax5.set_xlabel('Text Token (Key)')
            ax5.set_ylabel('Text Token (Query)')

            # 添加文本token标签
            if len(text_tokens) > num_image_patches:
                text_labels = text_tokens[num_image_patches:]
                if len(text_labels) <= 20:
                    ax5.set_xticklabels(text_labels, rotation=45, fontsize=8)
                    ax5.set_yticklabels(text_labels, rotation=0, fontsize=8)

        # 6. 统计信息
        ax6 = plt.subplot(2, 3, 6)
        ax6.axis('off')

        # 显示分析统计
        stats_text = f"Layer {layer_idx+1}: {layer_name}\n\n"
        stats_text += f"Matrix Shape: {attention_matrix.shape}\n"
        stats_text += f"Image Patches: {num_image_patches}\n"
        stats_text += f"Text Tokens: {analysis.get('num_text_tokens', 0)}\n\n"

        if 'attention_patterns' in analysis:
            for pattern_name, pattern_stats in analysis['attention_patterns'].items():
                stats_text += f"{pattern_name.replace('_', ' ').title()}:\n"
                stats_text += f"  Mean: {pattern_stats.get('mean', 0):.4f}\n"
                stats_text += f"  Max: {pattern_stats.get('max', 0):.4f}\n"
                stats_text += f"  Std: {pattern_stats.get('std', 0):.4f}\n\n"

        ax6.text(0.1, 0.9, stats_text, transform=ax6.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace')

        plt.tight_layout()

        # 保存图片
        filename = f"layer_{layer_idx+1:02d}_{layer_name.replace('.', '_').replace('/', '_')}.png"
        filepath = os.path.join(output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"已保存: {filepath}")

    def _generate_summary_report(self, attention_data: Dict[str, Any], output_dir: str):
        """生成汇总报告"""

        # 创建汇总统计图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('PaliGemma Attention Analysis Summary', fontsize=16, fontweight='bold')

        attention_weights = attention_data['attention_weights']
        layer_names = list(attention_weights.keys())

        # 收集统计数据
        image_to_image_means = []
        image_to_text_means = []
        text_to_image_means = []
        text_to_text_means = []

        for layer_data in attention_weights.values():
            analysis = layer_data['analysis']
            patterns = analysis.get('attention_patterns', {})

            image_to_image_means.append(patterns.get('image_to_image', {}).get('mean', 0))
            image_to_text_means.append(patterns.get('image_to_text', {}).get('mean', 0))
            text_to_image_means.append(patterns.get('text_to_image', {}).get('mean', 0))
            text_to_text_means.append(patterns.get('text_to_text', {}).get('mean', 0))

        # 1. 各层注意力强度对比
        ax1 = axes[0, 0]
        x = range(len(layer_names))
        ax1.plot(x, image_to_image_means, 'o-', label='Image→Image', color='red')
        ax1.plot(x, image_to_text_means, 's-', label='Image→Text', color='purple')
        ax1.plot(x, text_to_image_means, '^-', label='Text→Image', color='green')
        ax1.plot(x, text_to_text_means, 'd-', label='Text→Text', color='orange')
        ax1.set_xlabel('Layer')
        ax1.set_ylabel('Mean Attention Weight')
        ax1.set_title('Attention Patterns Across Layers')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 注意力模式分布
        ax2 = axes[0, 1]
        pattern_means = [
            np.mean(image_to_image_means),
            np.mean(image_to_text_means),
            np.mean(text_to_image_means),
            np.mean(text_to_text_means)
        ]
        pattern_labels = ['Image→Image', 'Image→Text', 'Text→Image', 'Text→Text']
        colors = ['red', 'purple', 'green', 'orange']

        bars = ax2.bar(pattern_labels, pattern_means, color=colors, alpha=0.7)
        ax2.set_ylabel('Average Attention Weight')
        ax2.set_title('Overall Attention Pattern Distribution')
        ax2.tick_params(axis='x', rotation=45)

        # 添加数值标签
        for bar, value in zip(bars, pattern_means):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                    f'{value:.4f}', ha='center', va='bottom')

        # 3. 层级注意力热图
        ax3 = axes[1, 0]
        attention_matrix = np.array([
            image_to_image_means,
            image_to_text_means,
            text_to_image_means,
            text_to_text_means
        ])

        sns.heatmap(attention_matrix,
                   xticklabels=[f'L{i+1}' for i in range(len(layer_names))],
                   yticklabels=pattern_labels,
                   cmap='viridis', annot=True, fmt='.3f', ax=ax3)
        ax3.set_title('Attention Heatmap Across Layers')
        ax3.set_xlabel('Layer')

        # 4. 模型信息
        ax4 = axes[1, 1]
        ax4.axis('off')

        info_text = f"Model Analysis Summary\n\n"
        info_text += f"Image: {attention_data['image_path']}\n"
        info_text += f"Prompt: {attention_data['text_prompt']}\n\n"
        info_text += f"Image Patches: {attention_data['num_image_patches']}\n"
        info_text += f"Text Tokens: {len(attention_data['text_tokens'])}\n"
        info_text += f"Total Layers: {len(attention_weights)}\n\n"
        info_text += f"Text Tokens:\n"

        # 显示前10个token
        tokens_to_show = attention_data['text_tokens'][:10]
        for i, token in enumerate(tokens_to_show):
            info_text += f"  {i}: {token}\n"

        if len(attention_data['text_tokens']) > 10:
            info_text += f"  ... and {len(attention_data['text_tokens']) - 10} more"

        ax4.text(0.1, 0.9, info_text, transform=ax4.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace')

        plt.tight_layout()

        # 保存汇总图
        summary_path = os.path.join(output_dir, "attention_summary.png")
        plt.savefig(summary_path, dpi=300, bbox_inches='tight')
        plt.close()

        # 保存JSON报告
        json_report = {
            'model_info': attention_data['model_info'],
            'input_info': {
                'image_path': attention_data['image_path'],
                'text_prompt': attention_data['text_prompt'],
                'num_image_patches': attention_data['num_image_patches'],
                'text_tokens': attention_data['text_tokens']
            },
            'layer_statistics': {}
        }

        for layer_name, layer_data in attention_weights.items():
            json_report['layer_statistics'][layer_name] = layer_data['analysis']

        json_path = os.path.join(output_dir, "attention_analysis.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_report, f, indent=2, ensure_ascii=False)

        print(f"汇总报告已保存: {summary_path}")
        print(f"详细分析已保存: {json_path}")
