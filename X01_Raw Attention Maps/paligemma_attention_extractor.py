#!/usr/bin/env python3
"""
PaliGemma专用注意力提取器
针对PaliGemma的特殊架构进行优化的注意力权重提取
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Any, Tuple, Optional
from transformers import AutoProcessor, PaliGemmaForConditionalGeneration
from PIL import Image
import warnings
warnings.filterwarnings('ignore')

class PaliGemmaAttentionExtractor:
    """PaliGemma专用注意力提取器"""
    
    def __init__(self, model_path: str, device: str = "cuda:0"):
        self.model_path = model_path
        self.device = device
        self.model = None
        self.processor = None
        
        # 注意力存储
        self.attention_data = {}
        self.hooks = []
        
        # PaliGemma特定配置
        self.vision_layers = []
        self.language_layers = []
        
        self._load_model()
        self._identify_attention_layers()
    
    def _load_model(self):
        """加载PaliGemma模型"""
        print(f"正在加载PaliGemma模型: {self.model_path}")
        
        self.model = PaliGemmaForConditionalGeneration.from_pretrained(
            self.model_path,
            torch_dtype=torch.float16,  # 改为float16避免BFloat16问题
            device_map=self.device,
            trust_remote_code=True,
            output_attentions=True  # 确保输出注意力权重
        )
        
        self.processor = AutoProcessor.from_pretrained(
            self.model_path,
            trust_remote_code=True
        )
        
        self.model.eval()
        print("✅ 模型加载完成")
    
    def _identify_attention_layers(self):
        """识别PaliGemma中的注意力层"""
        print("正在识别注意力层...")
        
        for name, module in self.model.named_modules():
            # Vision Transformer的注意力层
            if 'vision_tower' in name and 'attention' in name:
                self.vision_layers.append(name)
            
            # Language Model的注意力层  
            elif 'language_model' in name and 'self_attn' in name:
                self.language_layers.append(name)
            
            # 多模态融合层的注意力
            elif 'multi_modal_projector' in name and 'attention' in name:
                self.language_layers.append(name)
        
        print(f"发现 {len(self.vision_layers)} 个视觉注意力层")
        print(f"发现 {len(self.language_layers)} 个语言注意力层")
        
        # 打印前几个层的名称作为示例
        if self.vision_layers:
            print("视觉层示例:", self.vision_layers[:3])
        if self.language_layers:
            print("语言层示例:", self.language_layers[:3])
    
    def extract_attention_with_hooks(self, image_path: str, text_prompt: str) -> Dict[str, Any]:
        """使用hooks提取注意力权重"""
        print(f"\n🖼️  图片: {image_path}")
        print(f"💬 提示: {text_prompt}")
        
        # 准备输入
        image = Image.open(image_path).convert("RGB")
        inputs = self.processor(
            images=image, 
            text=text_prompt, 
            return_tensors="pt"
        ).to(self.device)
        
        # 获取输入信息
        input_ids = inputs['input_ids']
        pixel_values = inputs['pixel_values']
        
        # 解析tokens
        text_tokens = self.processor.tokenizer.convert_ids_to_tokens(input_ids[0].cpu().numpy())
        
        # 计算图片patch数量
        vision_config = self.model.config.vision_config
        image_size = vision_config.image_size
        patch_size = vision_config.patch_size
        num_patches = (image_size // patch_size) ** 2
        
        print(f"📊 图片patches: {num_patches}")
        print(f"📊 文本tokens: {len(text_tokens)}")
        
        # 注册hooks
        self._register_attention_hooks()
        
        try:
            print("🔄 运行推理...")
            with torch.no_grad():
                outputs = self.model(**inputs, output_attentions=True)
            
            print("✅ 推理完成，处理注意力数据...")
            
            # 处理收集的注意力数据
            processed_data = self._process_collected_attention()
            
            # 添加元数据
            processed_data.update({
                'text_tokens': text_tokens,
                'num_image_patches': num_patches,
                'image_path': image_path,
                'text_prompt': text_prompt,
                'input_shape': {
                    'input_ids': input_ids.shape,
                    'pixel_values': pixel_values.shape
                }
            })
            
            return processed_data
            
        finally:
            self._remove_hooks()
    
    def extract_attention_direct(self, image_path: str, text_prompt: str) -> Dict[str, Any]:
        """直接从模型输出提取注意力权重"""
        print(f"\n🖼️  图片: {image_path}")
        print(f"💬 提示: {text_prompt}")
        
        # 准备输入
        image = Image.open(image_path).convert("RGB")
        inputs = self.processor(
            images=image, 
            text=text_prompt, 
            return_tensors="pt"
        ).to(self.device)
        
        # 获取输入信息
        input_ids = inputs['input_ids']
        text_tokens = self.processor.tokenizer.convert_ids_to_tokens(input_ids[0].cpu().numpy())
        
        # 计算图片patch数量
        vision_config = self.model.config.vision_config
        num_patches = (vision_config.image_size // vision_config.patch_size) ** 2
        
        print(f"📊 图片patches: {num_patches}")
        print(f"📊 文本tokens: {len(text_tokens)}")
        
        print("🔄 运行推理...")
        with torch.no_grad():
            outputs = self.model(**inputs, output_attentions=True)
        
        print("✅ 推理完成，提取注意力权重...")
        
        # 提取注意力权重
        attention_weights = {}
        
        # 从outputs中提取注意力权重
        if hasattr(outputs, 'attentions') and outputs.attentions is not None:
            for i, attention in enumerate(outputs.attentions):
                layer_name = f"language_layer_{i}"
                attention_weights[layer_name] = {
                    'attention_matrix': attention[0].cpu().numpy(),  # [num_heads, seq_len, seq_len]
                    'num_heads': attention.shape[1],
                    'shape': attention.shape
                }
        
        # 如果有vision attention
        if hasattr(outputs, 'vision_attentions') and outputs.vision_attentions is not None:
            for i, attention in enumerate(outputs.vision_attentions):
                layer_name = f"vision_layer_{i}"
                attention_weights[layer_name] = {
                    'attention_matrix': attention[0].cpu().numpy(),
                    'num_heads': attention.shape[1],
                    'shape': attention.shape
                }
        
        # 处理注意力权重
        processed_weights = {}
        for layer_name, layer_data in attention_weights.items():
            attention_matrix = layer_data['attention_matrix']
            
            # 平均所有注意力头
            if len(attention_matrix.shape) == 3:  # [num_heads, seq_len, seq_len]
                avg_attention = attention_matrix.mean(axis=0)
            else:
                avg_attention = attention_matrix
            
            # 分析注意力模式
            analysis = self._analyze_attention_patterns(
                avg_attention, num_patches, len(text_tokens)
            )
            
            processed_weights[layer_name] = {
                'attention_matrix': avg_attention,
                'original_shape': attention_matrix.shape,
                'num_heads': layer_data['num_heads'],
                'analysis': analysis
            }
        
        return {
            'attention_weights': processed_weights,
            'text_tokens': text_tokens,
            'num_image_patches': num_patches,
            'image_path': image_path,
            'text_prompt': text_prompt,
            'model_info': {
                'num_layers': len(processed_weights),
                'layer_names': list(processed_weights.keys())
            }
        }
    
    def _register_attention_hooks(self):
        """注册注意力hooks"""
        print("📌 注册attention hooks...")
        
        def create_hook(layer_name):
            def hook_fn(module, input, output):
                try:
                    # 尝试提取注意力权重
                    if isinstance(output, tuple) and len(output) >= 2:
                        attention_weights = output[1]
                        if attention_weights is not None:
                            # 转换为float32避免BFloat16问题
                            attention_float = attention_weights.detach().float().cpu()
                            self.attention_data[layer_name] = attention_float
                            print(f"✅ 捕获 {layer_name}: {attention_weights.shape}")
                except Exception as e:
                    print(f"❌ Hook错误 {layer_name}: {e}")
            return hook_fn
        
        # 注册vision层hooks
        for layer_name in self.vision_layers[:5]:  # 限制数量避免过多
            try:
                module = dict(self.model.named_modules())[layer_name]
                hook = create_hook(layer_name)
                handle = module.register_forward_hook(hook)
                self.hooks.append(handle)
            except KeyError:
                continue
        
        # 注册language层hooks
        for layer_name in self.language_layers[:10]:  # 限制数量
            try:
                module = dict(self.model.named_modules())[layer_name]
                hook = create_hook(layer_name)
                handle = module.register_forward_hook(hook)
                self.hooks.append(handle)
            except KeyError:
                continue
        
        print(f"📌 已注册 {len(self.hooks)} 个hooks")
    
    def _process_collected_attention(self) -> Dict[str, Any]:
        """处理收集到的注意力数据"""
        processed = {}

        for layer_name, attention in self.attention_data.items():
            try:
                if len(attention.shape) == 4:  # [batch, heads, seq, seq]
                    # 平均所有头
                    avg_attention = attention[0].mean(dim=0).numpy()

                    # 分析注意力模式
                    analysis = self._analyze_attention_patterns(
                        avg_attention, 256, 5  # 假设256个patches，5个文本tokens
                    )

                    processed[layer_name] = {
                        'attention_matrix': avg_attention,
                        'original_shape': attention.shape,
                        'num_heads': attention.shape[1],
                        'analysis': analysis
                    }

            except Exception as e:
                print(f"❌ 处理 {layer_name} 时出错: {e}")

        return {'attention_weights': processed}
    
    def _analyze_attention_patterns(self, attention_matrix: np.ndarray, 
                                  num_patches: int, num_tokens: int) -> Dict[str, Any]:
        """分析注意力模式"""
        seq_len = attention_matrix.shape[0]
        
        # 基本统计
        analysis = {
            'matrix_shape': attention_matrix.shape,
            'total_sequence_length': seq_len,
            'num_image_patches': num_patches,
            'num_text_tokens': num_tokens,
            'overall_stats': {
                'mean': float(attention_matrix.mean()),
                'std': float(attention_matrix.std()),
                'max': float(attention_matrix.max()),
                'min': float(attention_matrix.min())
            }
        }
        
        # 如果序列包含图片和文本
        if seq_len > num_patches:
            # 分离不同区域的注意力
            img_to_img = attention_matrix[:num_patches, :num_patches]
            img_to_text = attention_matrix[:num_patches, num_patches:]
            text_to_img = attention_matrix[num_patches:, :num_patches]
            text_to_text = attention_matrix[num_patches:, num_patches:]
            
            analysis['attention_patterns'] = {
                'image_to_image': {
                    'mean': float(img_to_img.mean()),
                    'std': float(img_to_img.std()),
                    'max': float(img_to_img.max())
                },
                'image_to_text': {
                    'mean': float(img_to_text.mean()),
                    'std': float(img_to_text.std()),
                    'max': float(img_to_text.max())
                },
                'text_to_image': {
                    'mean': float(text_to_img.mean()),
                    'std': float(text_to_img.std()),
                    'max': float(text_to_img.max())
                },
                'text_to_text': {
                    'mean': float(text_to_text.mean()),
                    'std': float(text_to_text.std()),
                    'max': float(text_to_text.max())
                }
            }
        
        return analysis
    
    def _remove_hooks(self):
        """移除所有hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
        self.attention_data.clear()
