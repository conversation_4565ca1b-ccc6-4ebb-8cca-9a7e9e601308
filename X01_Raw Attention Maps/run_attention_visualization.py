#!/usr/bin/env python3
"""
运行PaliGemma注意力可视化
"""

import os
import sys
import argparse
from datetime import datetime
from attention_visualizer import AttentionVisualizer

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PaliGemma注意力可视化')
    parser.add_argument('--model_path', type=str, 
                       default='/home/<USER>/dataset/X/models/PaliGemma',
                       help='PaliGemma模型路径')
    parser.add_argument('--image_path', type=str,
                       default='/home/<USER>/dataset/X/coke.png',
                       help='输入图片路径')
    parser.add_argument('--text_prompt', type=str,
                       default='pick coke can',
                       help='文本提示')
    parser.add_argument('--output_dir', type=str,
                       default='attention_visualizations',
                       help='输出目录')
    parser.add_argument('--device', type=str,
                       default='cuda:0',
                       help='设备 (cuda:0, cpu等)')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.image_path):
        print(f"错误: 图片文件不存在: {args.image_path}")
        return
    
    if not os.path.exists(args.model_path):
        print(f"错误: 模型路径不存在: {args.model_path}")
        return
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"{args.output_dir}_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    print("="*60)
    print("PaliGemma 注意力可视化")
    print("="*60)
    print(f"模型路径: {args.model_path}")
    print(f"图片路径: {args.image_path}")
    print(f"文本提示: {args.text_prompt}")
    print(f"输出目录: {output_dir}")
    print(f"设备: {args.device}")
    print("="*60)
    
    try:
        # 初始化可视化器
        visualizer = AttentionVisualizer(args.model_path, args.device)
        
        # 提取注意力图
        print("\n步骤1: 提取注意力权重...")
        attention_data = visualizer.extract_attention_maps(args.image_path, args.text_prompt)
        
        # 生成可视化
        print("\n步骤2: 生成可视化图表...")
        visualizer.visualize_attention_maps(attention_data, output_dir)
        
        print("\n" + "="*60)
        print("✅ 注意力可视化完成!")
        print(f"📁 结果保存在: {output_dir}")
        print("="*60)
        
        # 显示简要统计
        attention_weights = attention_data['attention_weights']
        print(f"\n📊 统计信息:")
        print(f"   - 分析层数: {len(attention_weights)}")
        print(f"   - 图片patches: {attention_data['num_image_patches']}")
        print(f"   - 文本tokens: {len(attention_data['text_tokens'])}")
        print(f"   - 生成文件: {len(attention_weights) + 2} 个")
        
        print(f"\n📋 生成的文件:")
        print(f"   - attention_summary.png (汇总图)")
        print(f"   - attention_analysis.json (详细数据)")
        for i, layer_name in enumerate(attention_weights.keys()):
            filename = f"layer_{i+1:02d}_{layer_name.replace('.', '_').replace('/', '_')}.png"
            print(f"   - {filename}")
        
    except Exception as e:
        print(f"\n❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
