# PaliGemma 注意力可视化工具

本工具包用于提取和可视化PaliGemma模型每一层对图片patch和文本token的注意力权重。

## 功能特性

- 🎯 **多种提取方法**: 支持直接提取、hooks捕获等多种注意力权重提取方式
- 🎨 **丰富可视化**: 生成热图、统计图表、分层分析等多种可视化
- 📊 **详细分析**: 分析图片-图片、图片-文本、文本-图片、文本-文本等注意力模式
- 🔧 **易于使用**: 提供简单的命令行接口和配置选项

## 文件结构

```
X01_Raw Attention Maps/
├── README.md                           # 本文件
├── attention_visualizer.py             # 通用注意力可视化器
├── paligemma_attention_extractor.py    # PaliGemma专用提取器
├── run_attention_visualization.py      # 基础运行脚本
└── run_complete_attention_analysis.py  # 完整分析脚本
```

## 环境要求

```bash
# 激活环境
source /home/<USER>/software/anaconda3/bin/activate spatialvla

# 确保安装了必要的包
pip install torch transformers pillow matplotlib seaborn numpy
```

## 快速开始

### 1. 基础使用

```bash
cd /home/<USER>/dataset/X/X01_Raw\ Attention\ Maps

# 使用默认参数运行
python run_complete_attention_analysis.py

# 或指定参数
python run_complete_attention_analysis.py \
    --model_path /home/<USER>/dataset/X/models/PaliGemma \
    --image_path /home/<USER>/dataset/X/coke.png \
    --text_prompt "pick coke can" \
    --device cuda:0
```

### 2. 自定义参数

```bash
# 使用自定义图片和提示
python run_complete_attention_analysis.py \
    --image_path /path/to/your/image.jpg \
    --text_prompt "your custom prompt" \
    --output_dir custom_output_folder
```

## 输出说明

运行完成后，会在输出目录中生成以下文件：

### 可视化文件
- `attention_summary.png` - 所有层的注意力模式汇总
- `layer_XX_layername.png` - 每一层的详细注意力热图
- `complete_analysis_results.json` - 详细的数值分析结果

### 可视化内容
每个层的可视化包含：
1. **完整注意力矩阵** - 显示所有位置间的注意力
2. **图片patch间注意力** - 图片内部的空间注意力模式
3. **文本对图片注意力** - 文本token如何关注图片区域
4. **图片对文本注意力** - 图片patch如何关注文本token
5. **文本间注意力** - 文本token之间的语言注意力
6. **统计信息** - 各种注意力模式的数值统计

## 技术细节

### PaliGemma架构理解
- **图片处理**: 224x224图片被分割为14x14=196个patches
- **文本处理**: 文本被tokenize为token序列
- **多模态融合**: 图片patches和文本tokens在transformer中联合处理

### 注意力提取方法
1. **直接提取**: 从模型的`output_attentions`直接获取
2. **Hooks捕获**: 通过forward hooks在推理过程中捕获
3. **层级识别**: 自动识别vision和language层的注意力模块

### 注意力分析维度
- **Image→Image**: 图片patch之间的空间关系
- **Image→Text**: 图片区域对文本理解的贡献
- **Text→Image**: 文本对图片区域的关注
- **Text→Text**: 文本内部的语言依赖关系

## 示例输出解读

### 注意力模式含义
- **高Image→Image注意力**: 表示模型在学习图片的空间结构
- **高Text→Image注意力**: 表示文本token强烈依赖图片信息
- **高Image→Text注意力**: 表示图片区域对文本生成有重要影响
- **高Text→Text注意力**: 表示强烈的语言建模模式

### 层级变化趋势
- **早期层**: 通常Image→Image注意力较强，学习基础视觉特征
- **中间层**: Text→Image和Image→Text注意力增强，进行多模态融合
- **后期层**: Text→Text注意力可能增强，专注于语言生成

## 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 使用CPU
   python run_complete_attention_analysis.py --device cpu
   ```

2. **模型加载失败**
   - 检查模型路径是否正确
   - 确保有足够的磁盘空间和内存

3. **注意力权重提取失败**
   - 脚本会自动尝试多种方法
   - 查看输出日志确定哪种方法成功

### 调试模式
```bash
# 添加详细输出
python -u run_complete_attention_analysis.py 2>&1 | tee analysis.log
```

## 扩展使用

### 批量处理
可以修改脚本来处理多张图片：
```python
images = ["image1.jpg", "image2.jpg", "image3.jpg"]
prompts = ["prompt1", "prompt2", "prompt3"]

for img, prompt in zip(images, prompts):
    run_attention_analysis(model_path, img, prompt, f"output_{img}")
```

### 自定义分析
可以基于提取的注意力权重进行自定义分析：
```python
# 加载分析结果
import json
with open("complete_analysis_results.json") as f:
    results = json.load(f)

# 进行自定义分析
# ...
```

## 参考资料

- [PaliGemma论文](https://arxiv.org/abs/2407.07726)
- [Transformers库文档](https://huggingface.co/docs/transformers)
- [注意力机制原理](https://arxiv.org/abs/1706.03762)
