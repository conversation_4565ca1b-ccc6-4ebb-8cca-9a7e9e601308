#!/usr/bin/env python3
"""
图像注意力映射器
将注意力权重映射回原始图像，显示每个patch的注意力强度
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image
import cv2
import json
from typing import Dict, List, Any, Tuple
import seaborn as sns

class ImageAttentionMapper:
    """图像注意力映射器"""

    def __init__(self, patch_size: int = 14, image_size: int = 224):
        """
        初始化映射器
        Args:
            patch_size: 每个patch的大小（像素）
            image_size: 输入图像的大小（像素）
        """
        self.patch_size = patch_size
        self.image_size = image_size
        self.patches_per_side = image_size // patch_size  # 16 for 224/14
        self.total_patches = self.patches_per_side ** 2  # 256 for 16x16

    def normalize_map_local(self, att_map: np.ndarray) -> np.ndarray:
        """局部 Min-Max 归一化注意力图到 [0, 1]（类似原版本的 normalize_map）"""
        min_val = np.min(att_map)
        max_val = np.max(att_map)
        if max_val > min_val:
            return (att_map - min_val) / (max_val - min_val)
        elif max_val == min_val and max_val > 0:
            return np.ones_like(att_map)
        else:
            return np.zeros_like(att_map)

    def normalize_map_global(self, att_map: np.ndarray, global_min: float, global_max: float) -> np.ndarray:
        """使用全局最小/最大值进行归一化到 [0, 1]（类似原版本的 normalize_map_global）"""
        if global_max > global_min:
            normalized = (np.clip(att_map, global_min, global_max) - global_min) / (global_max - global_min)
            return normalized
        elif global_max == global_min and global_max > 0:
            return np.ones_like(att_map) * (att_map >= global_max)
        else:
            return np.zeros_like(att_map)
        
    def create_attention_heatmap_overlay(self, image_path: str, attention_weights: np.ndarray, 
                                       layer_name: str, output_dir: str, 
                                       attention_type: str = "average") -> str:
        """
        创建注意力热图叠加在原图上
        
        Args:
            image_path: 原始图像路径
            attention_weights: 注意力权重矩阵 [seq_len, seq_len]
            layer_name: 层名称
            output_dir: 输出目录
            attention_type: 注意力类型 ("average", "to_text", "from_text", "self_attention")
        
        Returns:
            保存的文件路径
        """
        # 加载原始图像
        original_image = Image.open(image_path).convert("RGB")
        original_image = original_image.resize((self.image_size, self.image_size))
        
        # 提取图像patch的注意力权重
        if attention_type == "average":
            # 平均所有注意力（包括对文本的注意力）
            patch_attention = attention_weights[:self.total_patches, :].mean(axis=1)
        elif attention_type == "self_attention":
            # 只看图像patch之间的自注意力
            patch_attention = attention_weights[:self.total_patches, :self.total_patches].mean(axis=1)
        elif attention_type == "to_text":
            # 图像patch对文本的注意力
            if attention_weights.shape[1] > self.total_patches:
                patch_attention = attention_weights[:self.total_patches, self.total_patches:].mean(axis=1)
            else:
                patch_attention = attention_weights[:self.total_patches, :].mean(axis=1)
        elif attention_type == "from_text":
            # 文本对图像patch的注意力
            if attention_weights.shape[0] > self.total_patches:
                patch_attention = attention_weights[self.total_patches:, :self.total_patches].mean(axis=0)
            else:
                patch_attention = attention_weights[:, :self.total_patches].mean(axis=0)
        else:
            patch_attention = attention_weights[:self.total_patches, :].mean(axis=1)
        
        # 检查并清理注意力权重
        patch_attention = np.nan_to_num(patch_attention, nan=0.0, posinf=0.0, neginf=0.0)

        # 调试信息：打印注意力值的统计信息
        print(f"    {attention_type} - Min: {patch_attention.min():.6f}, Max: {patch_attention.max():.6f}, "
              f"Mean: {patch_attention.mean():.6f}, Std: {patch_attention.std():.6f}")

        # 检查是否所有值都相同或接近相同
        if patch_attention.std() < 1e-8:
            print(f"    警告: {attention_type} 模式的注意力值几乎相同，添加小量噪声以改善可视化")
            # 添加极小的随机噪声以改善可视化
            patch_attention = patch_attention + np.random.normal(0, 1e-6, patch_attention.shape)

        # 将1D注意力权重重塑为2D网格
        attention_grid = patch_attention.reshape(self.patches_per_side, self.patches_per_side)

        # 确保数据类型正确
        attention_grid = attention_grid.astype(np.float64)

        # 创建可视化
        fig, axes = plt.subplots(1, 4, figsize=(20, 5))

        # 1. 原始图像
        axes[0].imshow(original_image)
        axes[0].set_title('Original Image')
        axes[0].axis('off')

        # 2. 注意力热图
        im1 = axes[1].imshow(attention_grid, cmap='viridis', interpolation='nearest')
        axes[1].set_title(f'Attention Heatmap\n({attention_type})')
        axes[1].axis('off')
        plt.colorbar(im1, ax=axes[1], fraction=0.046, pad=0.04)

        # 3. 热图叠加在原图上 - patch级别的离散显示
        # 创建叠加图
        overlay = np.array(original_image).astype(np.float32) / 255.0

        # 归一化注意力权重到0-1，处理边界情况
        grid_min = attention_grid.min()
        grid_max = attention_grid.max()
        grid_range = grid_max - grid_min

        if grid_range > 1e-8:  # 有足够的变化范围
            attention_norm = (attention_grid - grid_min) / grid_range
        else:
            # 如果范围太小，使用更敏感的归一化
            print(f"    注意: {attention_type} 模式值范围很小 ({grid_range:.8f})，使用百分位数归一化")
            # 使用百分位数进行更敏感的归一化
            p5 = np.percentile(attention_grid, 5)
            p95 = np.percentile(attention_grid, 95)
            if p95 - p5 > 1e-8:
                attention_norm = np.clip((attention_grid - p5) / (p95 - p5), 0, 1)
            else:
                # 最后的备选方案：使用相对于均值的偏差
                mean_val = attention_grid.mean()
                std_val = attention_grid.std()
                if std_val > 1e-8:
                    attention_norm = np.clip((attention_grid - mean_val) / (3 * std_val) + 0.5, 0, 1)
                else:
                    attention_norm = np.ones_like(attention_grid) * 0.5

        # 创建patch级别的叠加图
        blended = overlay.copy()

        # 为每个patch单独着色
        for i in range(self.patches_per_side):
            for j in range(self.patches_per_side):
                # 计算patch在原图中的位置
                start_y = i * self.patch_size
                end_y = start_y + self.patch_size
                start_x = j * self.patch_size
                end_x = start_x + self.patch_size

                # 获取该patch的注意力值
                attention_val = attention_norm[i, j]

                # 使用viridis colormap获取颜色 (深紫色到黄色的自然渐变)
                color = np.array(plt.cm.viridis(attention_val)[:3])  # 转换为numpy数组

                # 设置透明度
                alpha = attention_val * 0.6  # 注意力越高越不透明

                # 混合该patch区域
                patch_region = overlay[start_y:end_y, start_x:end_x]
                blended[start_y:end_y, start_x:end_x] = patch_region * (1 - alpha) + color * alpha
        
        axes[2].imshow(blended)
        axes[2].set_title('Attention Overlay')
        axes[2].axis('off')
        
        # 4. 带网格的patch分析
        axes[3].imshow(original_image)
        
        # 绘制patch网格和注意力强度
        for i in range(self.patches_per_side):
            for j in range(self.patches_per_side):
                # 计算patch位置
                x = j * self.patch_size
                y = i * self.patch_size
                
                # 获取该patch的注意力值
                attention_val = attention_grid[i, j]
                
                # 根据注意力强度设置颜色
                color_intensity = (attention_val - attention_grid.min()) / (attention_grid.max() - attention_grid.min())
                color = plt.cm.hot(color_intensity)
                
                # 绘制patch边框
                rect = patches.Rectangle((x, y), self.patch_size, self.patch_size,
                                       linewidth=1, edgecolor=color, facecolor='none', alpha=0.8)
                axes[3].add_patch(rect)
                
                # 在patch中心显示注意力值（只显示高注意力的）
                if color_intensity > 0.7:  # 只显示前30%的高注意力patch
                    axes[3].text(x + self.patch_size//2, y + self.patch_size//2, 
                               f'{attention_val:.3f}', 
                               ha='center', va='center', fontsize=6, 
                               color='white', weight='bold',
                               bbox=dict(boxstyle='round,pad=0.2', facecolor='black', alpha=0.7))
        
        axes[3].set_title(f'Patch Analysis\n(High attention values shown)')
        axes[3].axis('off')
        
        # 添加整体标题
        fig.suptitle(f'{layer_name} - {attention_type.replace("_", " ").title()} Attention Mapping', 
                    fontsize=14, fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图像
        filename = f"{layer_name}_{attention_type}_image_mapping.png"
        filepath = os.path.join(output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filepath
    
    def create_top_patches_visualization(self, image_path: str, attention_weights: np.ndarray,
                                       layer_name: str, output_dir: str, top_k: int = 10) -> str:
        """
        创建top-k最高注意力patch的可视化
        
        Args:
            image_path: 原始图像路径
            attention_weights: 注意力权重矩阵
            layer_name: 层名称
            output_dir: 输出目录
            top_k: 显示前k个最高注意力的patch
        
        Returns:
            保存的文件路径
        """
        # 加载原始图像
        original_image = Image.open(image_path).convert("RGB")
        original_image = original_image.resize((self.image_size, self.image_size))
        
        # 提取图像patch的平均注意力
        patch_attention = attention_weights[:self.total_patches, :].mean(axis=1)
        
        # 找到top-k最高注意力的patch
        top_indices = np.argsort(patch_attention)[-top_k:][::-1]  # 降序排列
        
        # 创建可视化
        fig, axes = plt.subplots(2, 6, figsize=(18, 6))
        
        # 第一行：显示完整图像和top patches位置
        axes[0, 0].imshow(original_image)
        axes[0, 0].set_title('Original Image\nwith Top Patches')
        axes[0, 0].axis('off')
        
        # 在原图上标记top patches
        for idx, patch_idx in enumerate(top_indices):
            # 计算patch在网格中的位置
            patch_row = patch_idx // self.patches_per_side
            patch_col = patch_idx % self.patches_per_side
            
            # 计算像素位置
            x = patch_col * self.patch_size
            y = patch_row * self.patch_size
            
            # 绘制高亮框
            color = plt.cm.viridis(idx / top_k)
            rect = patches.Rectangle((x, y), self.patch_size, self.patch_size,
                                   linewidth=3, edgecolor=color, facecolor='none')
            axes[0, 0].add_patch(rect)
            
            # 添加标号
            axes[0, 0].text(x + 2, y + 2, str(idx + 1), 
                          fontsize=10, color='white', weight='bold',
                          bbox=dict(boxstyle='circle', facecolor=color, alpha=0.8))
        
        # 显示注意力热图
        attention_grid = patch_attention.reshape(self.patches_per_side, self.patches_per_side)
        im = axes[0, 1].imshow(attention_grid, cmap='viridis', interpolation='nearest')
        axes[0, 1].set_title('Attention Heatmap')
        axes[0, 1].axis('off')
        plt.colorbar(im, ax=axes[0, 1], fraction=0.046, pad=0.04)
        
        # 显示前5个最高注意力的patch细节
        for i in range(min(5, top_k)):
            if i + 2 < 6:  # 确保不超出axes范围
                patch_idx = top_indices[i]
                patch_row = patch_idx // self.patches_per_side
                patch_col = patch_idx % self.patches_per_side
                
                # 提取patch区域
                x = patch_col * self.patch_size
                y = patch_row * self.patch_size
                patch_img = original_image.crop((x, y, x + self.patch_size, y + self.patch_size))
                
                axes[0, i + 2].imshow(patch_img)
                axes[0, i + 2].set_title(f'Patch #{i+1}\nAttn: {patch_attention[patch_idx]:.4f}')
                axes[0, i + 2].axis('off')
        
        # 隐藏未使用的subplot
        for i in range(min(5, top_k) + 2, 6):
            axes[0, i].axis('off')
        
        # 第二行：显示注意力统计
        # 注意力分布直方图
        axes[1, 0].hist(patch_attention, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        axes[1, 0].axvline(patch_attention.mean(), color='red', linestyle='--', 
                          label=f'Mean: {patch_attention.mean():.4f}')
        axes[1, 0].set_title('Attention Distribution')
        axes[1, 0].set_xlabel('Attention Weight')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].legend()
        
        # Top-k注意力值柱状图
        top_values = patch_attention[top_indices]
        axes[1, 1].bar(range(1, len(top_values) + 1), top_values, 
                      color=plt.cm.viridis(np.linspace(0, 1, len(top_values))))
        axes[1, 1].set_title(f'Top {top_k} Attention Values')
        axes[1, 1].set_xlabel('Patch Rank')
        axes[1, 1].set_ylabel('Attention Weight')
        
        # 显示统计信息
        axes[1, 2].axis('off')
        stats_text = f"""Attention Statistics:
        
Mean: {patch_attention.mean():.4f}
Std:  {patch_attention.std():.4f}
Max:  {patch_attention.max():.4f}
Min:  {patch_attention.min():.4f}

Top {top_k} Patches:
"""
        for i, (idx, val) in enumerate(zip(top_indices[:5], top_values[:5])):
            row = idx // self.patches_per_side
            col = idx % self.patches_per_side
            stats_text += f"{i+1}. Patch({row},{col}): {val:.4f}\n"
        
        axes[1, 2].text(0.1, 0.9, stats_text, transform=axes[1, 2].transAxes, 
                       fontsize=10, verticalalignment='top', fontfamily='monospace')
        
        # 隐藏剩余的subplot
        for i in range(3, 6):
            axes[1, i].axis('off')
        
        plt.tight_layout()
        
        # 保存图像
        filename = f"{layer_name}_top_patches_analysis.png"
        filepath = os.path.join(output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        return filepath
    
    def process_attention_data(self, attention_data: Dict[str, Any], output_dir: str,
                             normalization_mode: str = "both") -> Dict[str, List[str]]:
        """
        处理完整的注意力数据，生成所有图像映射
        
        Args:
            attention_data: 注意力分析数据
            output_dir: 输出目录
            
        Returns:
            生成的文件路径字典
        """
        image_path = attention_data['image_path']
        attention_weights = attention_data['attention_weights']
        
        # 创建图像映射子目录
        image_mapping_dir = os.path.join(output_dir, "image_attention_mapping")
        os.makedirs(image_mapping_dir, exist_ok=True)
        
        generated_files = {
            'heatmap_overlays': [],
            'top_patches': []
        }
        
        print(f"🎨 正在生成图像注意力映射...")
        
        # 为每一层生成图像映射
        for layer_name, layer_data in attention_weights.items():
            attention_matrix = layer_data['attention_matrix']
            
            print(f"  处理 {layer_name}...")
            
            # 生成不同类型的注意力热图叠加
            attention_types = ['average', 'self_attention']
            if attention_matrix.shape[0] > self.total_patches:
                attention_types.extend(['to_text', 'from_text'])
            
            for att_type in attention_types:
                try:
                    filepath = self.create_attention_heatmap_overlay(
                        image_path, attention_matrix, layer_name, 
                        image_mapping_dir, att_type
                    )
                    generated_files['heatmap_overlays'].append(filepath)
                except Exception as e:
                    print(f"    ❌ 生成 {att_type} 热图失败: {e}")
            
            # 生成top patches分析
            try:
                filepath = self.create_top_patches_visualization(
                    image_path, attention_matrix, layer_name, 
                    image_mapping_dir, top_k=10
                )
                generated_files['top_patches'].append(filepath)
            except Exception as e:
                print(f"    ❌ 生成top patches分析失败: {e}")
        
        print(f"✅ 图像注意力映射完成！生成了 {len(generated_files['heatmap_overlays']) + len(generated_files['top_patches'])} 个文件")

        # 生成总集图片
        print("🖼️  正在生成总集图片...")
        summary_files = self._create_summary_grids(attention_data, image_mapping_dir)
        generated_files['summary_grids'] = summary_files

        return generated_files

    def _create_summary_grids(self, attention_data: Dict[str, Any], output_dir: str) -> List[str]:
        """创建四种注意力类型的总集图片"""
        attention_weights = attention_data['attention_weights']
        image_path = attention_data['image_path']

        # 四种注意力类型
        attention_types = ['average', 'self_attention', 'to_text', 'from_text']
        summary_files = []

        for att_type in attention_types:
            print(f"  生成 {att_type} 总集图...")

            # 计算网格大小 (尽量接近正方形)
            num_layers = len(attention_weights)
            cols = int(np.ceil(np.sqrt(num_layers)))
            rows = int(np.ceil(num_layers / cols))

            # 创建大图
            fig, axes = plt.subplots(rows, cols, figsize=(cols * 4, rows * 4))
            if rows == 1:
                axes = axes.reshape(1, -1)
            elif cols == 1:
                axes = axes.reshape(-1, 1)

            fig.suptitle(f'All Layers - {att_type.replace("_", " ").title()} Attention',
                        fontsize=16, fontweight='bold')

            layer_idx = 0
            for i in range(rows):
                for j in range(cols):
                    ax = axes[i, j]

                    if layer_idx < num_layers:
                        layer_name = list(attention_weights.keys())[layer_idx]
                        attention_matrix = attention_weights[layer_name]['attention_matrix']

                        # 提取对应类型的注意力
                        patch_attention = self._extract_attention_by_type(
                            attention_matrix, att_type
                        )

                        # 清理数据
                        patch_attention = np.nan_to_num(patch_attention, nan=0.0, posinf=0.0, neginf=0.0)

                        # 调试信息：打印注意力值的统计信息（仅对第一层显示）
                        if layer_idx == 0:
                            print(f"    总集图 {att_type} - Min: {patch_attention.min():.6f}, Max: {patch_attention.max():.6f}, "
                                  f"Mean: {patch_attention.mean():.6f}, Std: {patch_attention.std():.6f}")

                        # 检查是否所有值都相同或接近相同
                        if patch_attention.std() < 1e-8:
                            if layer_idx == 0:  # 只对第一层显示警告
                                print(f"    总集图警告: {att_type} 模式的注意力值几乎相同，添加小量噪声以改善可视化")
                            # 添加极小的随机噪声以改善可视化
                            patch_attention = patch_attention + np.random.normal(0, 1e-6, patch_attention.shape)

                        # 重塑为网格
                        attention_grid = patch_attention.reshape(self.patches_per_side, self.patches_per_side)
                        attention_grid = attention_grid.astype(np.float64)

                        # 加载原图作为背景
                        original_image = Image.open(image_path).convert("RGB")
                        original_image = original_image.resize((self.image_size, self.image_size))
                        background = np.array(original_image).astype(np.float32) / 255.0

                        # 归一化注意力权重，处理边界情况
                        grid_min = attention_grid.min()
                        grid_max = attention_grid.max()
                        grid_range = grid_max - grid_min

                        if grid_range > 1e-8:  # 有足够的变化范围
                            attention_norm = (attention_grid - grid_min) / grid_range
                        else:
                            # 如果范围太小，使用百分位数归一化
                            p5 = np.percentile(attention_grid, 5)
                            p95 = np.percentile(attention_grid, 95)
                            if p95 - p5 > 1e-8:
                                attention_norm = np.clip((attention_grid - p5) / (p95 - p5), 0, 1)
                            else:
                                # 使用相对于均值的偏差
                                mean_val = attention_grid.mean()
                                std_val = attention_grid.std()
                                if std_val > 1e-8:
                                    attention_norm = np.clip((attention_grid - mean_val) / (3 * std_val) + 0.5, 0, 1)
                                else:
                                    attention_norm = np.ones_like(attention_grid) * 0.5

                        # 创建叠加图
                        overlay = background.copy()

                        # 为每个patch着色
                        for pi in range(self.patches_per_side):
                            for pj in range(self.patches_per_side):
                                start_y = pi * self.patch_size
                                end_y = start_y + self.patch_size
                                start_x = pj * self.patch_size
                                end_x = start_x + self.patch_size

                                attention_val = attention_norm[pi, pj]
                                color = np.array(plt.cm.viridis(attention_val)[:3])  # 深紫色到黄色的自然渐变
                                alpha = attention_val * 0.5  # 总集图用更低的透明度

                                patch_region = background[start_y:end_y, start_x:end_x]
                                overlay[start_y:end_y, start_x:end_x] = patch_region * (1 - alpha) + color * alpha

                        # 显示叠加图
                        ax.imshow(overlay)
                        ax.set_title(f'Layer {layer_idx+1}', fontsize=10)
                        ax.axis('off')

                        layer_idx += 1
                    else:
                        # 隐藏多余的subplot
                        ax.axis('off')

            plt.tight_layout()

            # 保存总集图
            filename = f"summary_grid_{att_type}.png"
            filepath = os.path.join(output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()

            summary_files.append(filepath)
            print(f"    ✅ 保存: {filename}")

        return summary_files

    def _extract_attention_by_type(self, attention_matrix: np.ndarray, attention_type: str) -> np.ndarray:
        """根据类型提取注意力权重"""
        if attention_type == "average":
            # 平均所有注意力（包括对文本的注意力）
            patch_attention = attention_matrix[:self.total_patches, :].mean(axis=1)
        elif attention_type == "self_attention":
            # 只看图像patch之间的自注意力
            patch_attention = attention_matrix[:self.total_patches, :self.total_patches].mean(axis=1)
        elif attention_type == "to_text":
            # 图像patch对文本的注意力
            if attention_matrix.shape[1] > self.total_patches:
                patch_attention = attention_matrix[:self.total_patches, self.total_patches:].mean(axis=1)
            else:
                patch_attention = attention_matrix[:self.total_patches, :].mean(axis=1)
        elif attention_type == "from_text":
            # 文本对图像patch的注意力
            if attention_matrix.shape[0] > self.total_patches:
                patch_attention = attention_matrix[self.total_patches:, :self.total_patches].mean(axis=0)
            else:
                patch_attention = attention_matrix[:, :self.total_patches].mean(axis=0)
        else:
            patch_attention = attention_matrix[:self.total_patches, :].mean(axis=1)

        return patch_attention
