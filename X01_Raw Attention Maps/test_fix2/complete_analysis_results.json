{"direct_method": {"attention_weights": {"language_layer_0": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0416259765625, "max": 0.7470703125, "min": 5.960464477539063e-08}, "attention_patterns": {"image_to_image": {"mean": 0.0033740997314453125, "std": 0.041168212890625, "max": 0.7470703125}, "image_to_text": {"mean": 0.045318603515625, "std": 0.0628662109375, "max": 0.444580078125}, "text_to_image": {"mean": 0.0016946792602539062, "std": 0.0088958740234375, "max": 0.158203125}, "text_to_text": {"mean": 0.188720703125, "std": 0.2041015625, "max": 0.62109375}}}, "num_heads": 8}, "language_layer_1": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0311737060546875, "max": 0.68212890625, "min": 3.337860107421875e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0019855499267578125, "std": 0.01360321044921875, "max": 0.429443359375}, "image_to_text": {"mean": 0.1639404296875, "std": 0.2052001953125, "max": 0.623046875}, "text_to_image": {"mean": 0.0017824172973632812, "std": 0.0019683837890625, "max": 0.0131988525390625}, "text_to_text": {"mean": 0.1812744140625, "std": 0.250244140625, "max": 0.68212890625}}}, "num_heads": 8}, "language_layer_2": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0391845703125, "max": 0.73193359375, "min": 9.298324584960938e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0014495849609375, "std": 0.00951385498046875, "max": 0.25146484375}, "image_to_text": {"mean": 0.2095947265625, "std": 0.287109375, "max": 0.73193359375}, "text_to_image": {"mean": 0.00160980224609375, "std": 0.0018434524536132812, "max": 0.018890380859375}, "text_to_text": {"mean": 0.196044921875, "std": 0.262939453125, "max": 0.72900390625}}}, "num_heads": 8}, "language_layer_3": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.040618896484375, "max": 0.76171875, "min": 4.172325134277344e-07}, "attention_patterns": {"image_to_image": {"mean": 0.001377105712890625, "std": 0.00830078125, "max": 0.319580078125}, "image_to_text": {"mean": 0.2158203125, "std": 0.302490234375, "max": 0.76171875}, "text_to_image": {"mean": 0.0015859603881835938, "std": 0.0018596649169921875, "max": 0.015777587890625}, "text_to_text": {"mean": 0.197998046875, "std": 0.265380859375, "max": 0.7587890625}}}, "num_heads": 8}, "language_layer_4": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.03143310546875, "max": 0.66552734375, "min": 4.172325134277344e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00164794921875, "std": 0.007694244384765625, "max": 0.2139892578125}, "image_to_text": {"mean": 0.1927490234375, "std": 0.2100830078125, "max": 0.65771484375}, "text_to_image": {"mean": 0.001567840576171875, "std": 0.00177764892578125, "max": 0.0103607177734375}, "text_to_text": {"mean": 0.1995849609375, "std": 0.22216796875, "max": 0.66552734375}}}, "num_heads": 8}, "language_layer_5": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0246734619140625, "max": 0.55517578125, "min": 4.708766937255859e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0022220611572265625, "std": 0.006275177001953125, "max": 0.1866455078125}, "image_to_text": {"mean": 0.143798828125, "std": 0.1712646484375, "max": 0.55517578125}, "text_to_image": {"mean": 0.001983642578125, "std": 0.002223968505859375, "max": 0.03570556640625}, "text_to_text": {"mean": 0.1641845703125, "std": 0.184326171875, "max": 0.50537109375}}}, "num_heads": 8}, "language_layer_6": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0283050537109375, "max": 0.607421875, "min": 5.781650543212891e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0020427703857421875, "std": 0.00579833984375, "max": 0.1861572265625}, "image_to_text": {"mean": 0.158935546875, "std": 0.2054443359375, "max": 0.607421875}, "text_to_image": {"mean": 0.0018053054809570312, "std": 0.0028362274169921875, "max": 0.05950927734375}, "text_to_text": {"mean": 0.1793212890625, "std": 0.1527099609375, "max": 0.472900390625}}}, "num_heads": 8}, "language_layer_7": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0286712646484375, "max": 0.69482421875, "min": 5.364418029785156e-07}, "attention_patterns": {"image_to_image": {"mean": 0.001956939697265625, "std": 0.0067138671875, "max": 0.357421875}, "image_to_text": {"mean": 0.1663818359375, "std": 0.2012939453125, "max": 0.69482421875}, "text_to_image": {"mean": 0.0019016265869140625, "std": 0.00238037109375, "max": 0.0333251953125}, "text_to_text": {"mean": 0.1710205078125, "std": 0.19384765625, "max": 0.58984375}}}, "num_heads": 8}, "language_layer_8": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0219573974609375, "max": 0.640625, "min": 1.7881393432617188e-06}, "attention_patterns": {"image_to_image": {"mean": 0.002483367919921875, "std": 0.005588531494140625, "max": 0.205078125}, "image_to_text": {"mean": 0.12139892578125, "std": 0.15771484375, "max": 0.640625}, "text_to_image": {"mean": 0.00220489501953125, "std": 0.0035037994384765625, "max": 0.037811279296875}, "text_to_text": {"mean": 0.145263671875, "std": 0.1806640625, "max": 0.5751953125}}}, "num_heads": 8}, "language_layer_9": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.022918701171875, "max": 0.58154296875, "min": 4.291534423828125e-06}, "attention_patterns": {"image_to_image": {"mean": 0.002384185791015625, "std": 0.0038604736328125, "max": 0.0802001953125}, "image_to_text": {"mean": 0.1298828125, "std": 0.1671142578125, "max": 0.58154296875}, "text_to_image": {"mean": 0.0024261474609375, "std": 0.005321502685546875, "max": 0.12744140625}, "text_to_text": {"mean": 0.1263427734375, "std": 0.186279296875, "max": 0.5068359375}}}, "num_heads": 8}, "language_layer_10": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0212249755859375, "max": 0.54638671875, "min": 6.377696990966797e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00244140625, "std": 0.00489044189453125, "max": 0.157470703125}, "image_to_text": {"mean": 0.125, "std": 0.1478271484375, "max": 0.52734375}, "text_to_image": {"mean": 0.002288818359375, "std": 0.00794219970703125, "max": 0.1656494140625}, "text_to_text": {"mean": 0.1380615234375, "std": 0.1640625, "max": 0.54638671875}}}, "num_heads": 8}, "language_layer_11": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.020355224609375, "max": 0.467529296875, "min": 3.2186508178710938e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00252532958984375, "std": 0.00484466552734375, "max": 0.2291259765625}, "image_to_text": {"mean": 0.1177978515625, "std": 0.143310546875, "max": 0.467529296875}, "text_to_image": {"mean": 0.0021800994873046875, "std": 0.0032939910888671875, "max": 0.051239013671875}, "text_to_text": {"mean": 0.1473388671875, "std": 0.141845703125, "max": 0.443603515625}}}, "num_heads": 8}, "language_layer_12": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0216827392578125, "max": 0.59228515625, "min": 4.231929779052734e-06}, "attention_patterns": {"image_to_image": {"mean": 0.002536773681640625, "std": 0.00733184814453125, "max": 0.2293701171875}, "image_to_text": {"mean": 0.1168212890625, "std": 0.1524658203125, "max": 0.59228515625}, "text_to_image": {"mean": 0.00238800048828125, "std": 0.0029296875, "max": 0.02886962890625}, "text_to_text": {"mean": 0.129638671875, "std": 0.10479736328125, "max": 0.349853515625}}}, "num_heads": 8}, "language_layer_13": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0231781005859375, "max": 0.57177734375, "min": 2.2649765014648438e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0023136138916015625, "std": 0.00492095947265625, "max": 0.175048828125}, "image_to_text": {"mean": 0.1358642578125, "std": 0.1636962890625, "max": 0.57177734375}, "text_to_image": {"mean": 0.00231170654296875, "std": 0.00460052490234375, "max": 0.0872802734375}, "text_to_text": {"mean": 0.1361083984375, "std": 0.134765625, "max": 0.427978515625}}}, "num_heads": 8}, "language_layer_14": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0263824462890625, "max": 0.65234375, "min": 1.5497207641601562e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00218963623046875, "std": 0.005191802978515625, "max": 0.1739501953125}, "image_to_text": {"mean": 0.146484375, "std": 0.1939697265625, "max": 0.65234375}, "text_to_image": {"mean": 0.0025920867919921875, "std": 0.00484466552734375, "max": 0.08544921875}, "text_to_text": {"mean": 0.11224365234375, "std": 0.09613037109375, "max": 0.292236328125}}}, "num_heads": 8}, "language_layer_15": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0272674560546875, "max": 0.71240234375, "min": 1.1920928955078125e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0021953582763671875, "std": 0.00640869140625, "max": 0.1597900390625}, "image_to_text": {"mean": 0.14599609375, "std": 0.2012939453125, "max": 0.71240234375}, "text_to_image": {"mean": 0.002216339111328125, "std": 0.00426483154296875, "max": 0.0987548828125}, "text_to_text": {"mean": 0.144287109375, "std": 0.15771484375, "max": 0.5029296875}}}, "num_heads": 8}, "language_layer_16": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.024688720703125, "max": 0.81591796875, "min": 1.430511474609375e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0024013519287109375, "std": 0.007289886474609375, "max": 0.471435546875}, "image_to_text": {"mean": 0.12841796875, "std": 0.1807861328125, "max": 0.81591796875}, "text_to_image": {"mean": 0.0028972625732421875, "std": 0.0054168701171875, "max": 0.1014404296875}, "text_to_text": {"mean": 0.0860595703125, "std": 0.123779296875, "max": 0.374267578125}}}, "num_heads": 8}, "language_layer_17": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.02947998046875, "max": 0.6552734375, "min": 2.2649765014648438e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0019626617431640625, "std": 0.00719451904296875, "max": 0.2197265625}, "image_to_text": {"mean": 0.165771484375, "std": 0.2115478515625, "max": 0.6552734375}, "text_to_image": {"mean": 0.0028133392333984375, "std": 0.005275726318359375, "max": 0.0809326171875}, "text_to_text": {"mean": 0.09332275390625, "std": 0.101806640625, "max": 0.289306640625}}}, "num_heads": 8}, "language_layer_18": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.03216552734375, "max": 0.70263671875, "min": 1.430511474609375e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0018787384033203125, "std": 0.007587432861328125, "max": 0.304931640625}, "image_to_text": {"mean": 0.1729736328125, "std": 0.2369384765625, "max": 0.70263671875}, "text_to_image": {"mean": 0.0024662017822265625, "std": 0.00415802001953125, "max": 0.051300048828125}, "text_to_text": {"mean": 0.1229248046875, "std": 0.1610107421875, "max": 0.46533203125}}}, "num_heads": 8}, "language_layer_19": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.03765869140625, "max": 0.78955078125, "min": 1.430511474609375e-06}, "attention_patterns": {"image_to_image": {"mean": 0.0015048980712890625, "std": 0.005657196044921875, "max": 0.265380859375}, "image_to_text": {"mean": 0.2049560546875, "std": 0.28271484375, "max": 0.78955078125}, "text_to_image": {"mean": 0.0023956298828125, "std": 0.004535675048828125, "max": 0.06805419921875}, "text_to_text": {"mean": 0.12890625, "std": 0.141845703125, "max": 0.39453125}}}, "num_heads": 8}, "language_layer_20": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0374755859375, "max": 0.7705078125, "min": 2.086162567138672e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00145721435546875, "std": 0.004360198974609375, "max": 0.11505126953125}, "image_to_text": {"mean": 0.208984375, "std": 0.279052734375, "max": 0.7705078125}, "text_to_image": {"mean": 0.0022373199462890625, "std": 0.003040313720703125, "max": 0.0280303955078125}, "text_to_text": {"mean": 0.1424560546875, "std": 0.2042236328125, "max": 0.5654296875}}}, "num_heads": 8}, "language_layer_21": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.044921875, "max": 0.86669921875, "min": 1.7881393432617188e-07}, "attention_patterns": {"image_to_image": {"mean": 0.0010194778442382812, "std": 0.003749847412109375, "max": 0.1632080078125}, "image_to_text": {"mean": 0.246337890625, "std": 0.33935546875, "max": 0.86669921875}, "text_to_image": {"mean": 0.0025157928466796875, "std": 0.00400543212890625, "max": 0.0814208984375}, "text_to_text": {"mean": 0.11859130859375, "std": 0.1605224609375, "max": 0.4189453125}}}, "num_heads": 8}, "language_layer_22": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0472412109375, "max": 0.919921875, "min": 1.1920928955078125e-07}, "attention_patterns": {"image_to_image": {"mean": 0.0008721351623535156, "std": 0.002513885498046875, "max": 0.1087646484375}, "image_to_text": {"mean": 0.259033203125, "std": 0.358154296875, "max": 0.919921875}, "text_to_image": {"mean": 0.002796173095703125, "std": 0.0035037994384765625, "max": 0.052459716796875}, "text_to_text": {"mean": 0.0947265625, "std": 0.1500244140625, "max": 0.379638671875}}}, "num_heads": 8}, "language_layer_23": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.04412841796875, "max": 0.81787109375, "min": 1.1324882507324219e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00102996826171875, "std": 0.0026073455810546875, "max": 0.1552734375}, "image_to_text": {"mean": 0.2454833984375, "std": 0.331787109375, "max": 0.81787109375}, "text_to_image": {"mean": 0.002643585205078125, "std": 0.004108428955078125, "max": 0.06060791015625}, "text_to_text": {"mean": 0.10772705078125, "std": 0.12939453125, "max": 0.357666015625}}}, "num_heads": 8}, "language_layer_24": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.04766845703125, "max": 0.861328125, "min": 1.7285346984863281e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00081634521484375, "std": 0.002559661865234375, "max": 0.1336669921875}, "image_to_text": {"mean": 0.263671875, "std": 0.359375, "max": 0.861328125}, "text_to_image": {"mean": 0.0023784637451171875, "std": 0.0030879974365234375, "max": 0.029937744140625}, "text_to_text": {"mean": 0.1302490234375, "std": 0.135986328125, "max": 0.408447265625}}}, "num_heads": 8}, "language_layer_25": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 259, "overall_stats": {"mean": 0.0038604736328125, "std": 0.0308074951171875, "max": 0.7060546875, "min": 1.3709068298339844e-06}, "attention_patterns": {"image_to_image": {"mean": 0.00189971923828125, "std": 0.0055999755859375, "max": 0.25341796875}, "image_to_text": {"mean": 0.1712646484375, "std": 0.2269287109375, "max": 0.7060546875}, "text_to_image": {"mean": 0.0023517608642578125, "std": 0.002899169921875, "max": 0.0297698974609375}, "text_to_text": {"mean": 0.132568359375, "std": 0.126220703125, "max": 0.346923828125}}}, "num_heads": 8}}, "text_tokens": ["<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<bos>", "coke", "\n"], "num_image_patches": 256, "image_path": "/home/<USER>/dataset/X/coke.png", "text_prompt": "coke", "model_info": {"num_layers": 26, "layer_names": ["language_layer_0", "language_layer_1", "language_layer_2", "language_layer_3", "language_layer_4", "language_layer_5", "language_layer_6", "language_layer_7", "language_layer_8", "language_layer_9", "language_layer_10", "language_layer_11", "language_layer_12", "language_layer_13", "language_layer_14", "language_layer_15", "language_layer_16", "language_layer_17", "language_layer_18", "language_layer_19", "language_layer_20", "language_layer_21", "language_layer_22", "language_layer_23", "language_layer_24", "language_layer_25"]}}, "hooks_method": {"attention_weights": {"language_model.model.layers.0.self_attn": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 5, "overall_stats": {"mean": 0.003861029865220189, "std": 0.04161570221185684, "max": 0.7471566796302795, "min": 4.470348358154297e-08}, "attention_patterns": {"image_to_image": {"mean": 0.0033750382717698812, "std": 0.04116542637348175, "max": 0.7471566796302795}, "image_to_text": {"mean": 0.04533231630921364, "std": 0.06286631524562836, "max": 0.4445333480834961}, "text_to_image": {"mean": 0.0016951020807027817, "std": 0.008895806968212128, "max": 0.15821337699890137}, "text_to_text": {"mean": 0.18868698179721832, "std": 0.20415781438350677, "max": 0.6212279796600342}}}, "num_heads": 8}, "language_model.model.layers.1.self_attn": {"shape": [259, 259], "analysis": {"matrix_shape": [259, 259], "total_sequence_length": 259, "num_image_patches": 256, "num_text_tokens": 5, "overall_stats": {"mean": 0.003861000295728445, "std": 0.031177133321762085, "max": 0.6819534301757812, "min": 3.3602118492126465e-06}, "attention_patterns": {"image_to_image": {"mean": 0.001985528040677309, "std": 0.013601302169263363, "max": 0.4295475482940674}, "image_to_text": {"mean": 0.16390152275562286, "std": 0.20516113936901093, "max": 0.6232147216796875}, "text_to_image": {"mean": 0.001782479346729815, "std": 0.0019696259405463934, "max": 0.013197585940361023}, "text_to_text": {"mean": 0.18120816349983215, "std": 0.2501537799835205, "max": 0.6819534301757812}}}, "num_heads": 8}}, "text_tokens": ["<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<bos>", "coke", "\n"], "num_image_patches": 256, "image_path": "/home/<USER>/dataset/X/coke.png", "text_prompt": "coke", "input_shape": {"input_ids": [1, 259], "pixel_values": [1, 3, 224, 224]}}, "general_method": {"attention_weights": {}, "text_tokens": ["<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<image>", "<bos>", "coke", "\n"], "num_image_patches": 256, "image_path": "/home/<USER>/dataset/X/coke.png", "text_prompt": "coke", "model_info": {"num_layers": 0, "layer_names": []}}}