#!/usr/bin/env python3
"""
完整的PaliGemma注意力分析运行脚本
结合多种方法提取和可视化注意力权重
"""

import os
import sys
import argparse
import traceback
from datetime import datetime
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from attention_visualizer import AttentionVisualizer
from paligemma_attention_extractor import PaliGemmaAttentionExtractor
from image_attention_mapper import ImageAttentionMapper
from transformers import AutoProcessor, PaliGemmaForConditionalGeneration
from PIL import Image
import torch
from typing import Dict, Any

def generate_model_response(attention_data: Dict[str, Any], image_path: str,
                          text_prompt: str, device: str) -> str:
    """生成模型的自然语言回答"""
    try:
        # 从attention_data中获取模型路径
        model_path = "/home/<USER>/dataset/X/models/PaliGemma"

        # 加载模型和处理器
        model = PaliGemmaForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map=device,
            trust_remote_code=True
        )

        processor = AutoProcessor.from_pretrained(
            model_path,
            trust_remote_code=True
        )

        # 准备输入
        image = Image.open(image_path).convert("RGB")
        inputs = processor(images=image, text=text_prompt, return_tensors="pt").to(device)

        # 生成回答
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=100,
                do_sample=False,
                temperature=0.0
            )

        # 解码回答
        generated_text = processor.decode(outputs[0], skip_special_tokens=True)

        # 提取模型的回答部分（去掉输入的prompt）
        if text_prompt in generated_text:
            response = generated_text.split(text_prompt)[-1].strip()
        else:
            response = generated_text.strip()

        return response

    except Exception as e:
        return f"生成回答时出错: {e}"

def run_attention_analysis(model_path: str, image_path: str, text_prompt: str,
                          output_dir: str, device: str = "cuda:0"):
    """运行完整的注意力分析"""
    
    print("🚀 开始PaliGemma注意力分析")
    print("="*80)
    print(f"📁 模型路径: {model_path}")
    print(f"🖼️  图片路径: {image_path}")
    print(f"💬 文本提示: {text_prompt}")
    print(f"📂 输出目录: {output_dir}")
    print(f"🔧 设备: {device}")
    print("="*80)
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    results = {}
    
    try:
        # 方法1: 使用专用提取器 (直接方法)
        print("\n📊 方法1: 使用PaliGemma专用提取器...")
        extractor = PaliGemmaAttentionExtractor(model_path, device)
        
        try:
            attention_data_direct = extractor.extract_attention_direct(image_path, text_prompt)
            results['direct_method'] = attention_data_direct
            print("✅ 直接提取方法成功")
        except Exception as e:
            print(f"❌ 直接提取方法失败: {e}")
            results['direct_method'] = None
        
        # 方法2: 使用hooks方法
        print("\n🪝 方法2: 使用hooks提取...")
        try:
            attention_data_hooks = extractor.extract_attention_with_hooks(image_path, text_prompt)
            results['hooks_method'] = attention_data_hooks
            print("✅ Hooks提取方法成功")
        except Exception as e:
            print(f"❌ Hooks提取方法失败: {e}")
            results['hooks_method'] = None
        
        # 方法3: 使用通用可视化器
        print("\n🎨 方法3: 使用通用可视化器...")
        try:
            visualizer = AttentionVisualizer(model_path, device)
            attention_data_general = visualizer.extract_attention_maps(image_path, text_prompt)
            results['general_method'] = attention_data_general
            print("✅ 通用可视化器成功")
        except Exception as e:
            print(f"❌ 通用可视化器失败: {e}")
            results['general_method'] = None
        
        # 选择最佳结果进行可视化
        best_result = None
        best_method = None
        
        for method_name, result in results.items():
            if result is not None and 'attention_weights' in result:
                if len(result['attention_weights']) > 0:
                    best_result = result
                    best_method = method_name
                    break
        
        if best_result is None:
            print("\n❌ 所有方法都失败了，无法提取注意力权重")
            return False
        
        print(f"\n🎯 使用 {best_method} 的结果进行可视化")
        print(f"📊 成功提取 {len(best_result['attention_weights'])} 层的注意力权重")
        
        # 生成可视化
        print("\n🎨 生成可视化图表...")
        if 'general_method' in results and results['general_method'] is not None:
            # 使用通用可视化器的可视化功能
            visualizer.visualize_attention_maps(best_result, output_dir)
        else:
            # 使用简化的可视化
            create_simple_visualizations(best_result, output_dir)

        # 生成图像注意力映射
        print("\n🖼️  生成图像注意力映射...")
        try:
            image_mapper = ImageAttentionMapper(patch_size=14, image_size=224)
            mapping_files = image_mapper.process_attention_data(best_result, output_dir)

            print(f"✅ 图像映射完成！生成了:")
            print(f"   - {len(mapping_files['heatmap_overlays'])} 个热图叠加文件")
            print(f"   - {len(mapping_files['top_patches'])} 个top patches分析文件")

        except Exception as e:
            print(f"❌ 图像映射生成失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 保存分析结果
        save_analysis_results(results, output_dir)

        # 生成模型的自然语言输出
        print("\n🤖 生成模型的自然语言回答...")
        try:
            model_response = generate_model_response(
                best_result, image_path, text_prompt, device
            )
            print(f"📝 模型回答: {model_response}")

            # 保存模型回答到文件
            response_file = os.path.join(output_dir, "model_response.txt")
            with open(response_file, 'w', encoding='utf-8') as f:
                f.write(f"输入图片: {image_path}\n")
                f.write(f"输入文本: {text_prompt}\n")
                f.write(f"模型回答: {model_response}\n")

            print(f"💾 模型回答已保存到: {response_file}")

        except Exception as e:
            print(f"❌ 生成模型回答失败: {e}")

        print("\n" + "="*80)
        print("✅ 注意力分析完成!")
        print(f"📁 结果保存在: {output_dir}")
        print("="*80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {e}")
        traceback.print_exc()
        return False

def create_simple_visualizations(attention_data, output_dir):
    """创建简化的可视化"""
    import matplotlib.pyplot as plt
    import seaborn as sns
    import numpy as np
    import json
    
    attention_weights = attention_data['attention_weights']
    
    print(f"📊 创建 {len(attention_weights)} 层的简化可视化...")
    
    for i, (layer_name, layer_data) in enumerate(attention_weights.items()):
        try:
            attention_matrix = layer_data['attention_matrix']
            
            # 创建热图
            plt.figure(figsize=(12, 10))
            sns.heatmap(attention_matrix, cmap='viridis', cbar=True)
            plt.title(f'Layer {i+1}: {layer_name}\nAttention Matrix')
            plt.xlabel('Key Position')
            plt.ylabel('Query Position')
            
            # 添加分界线
            num_patches = attention_data.get('num_image_patches', 196)
            if attention_matrix.shape[0] > num_patches:
                plt.axhline(y=num_patches, color='red', linestyle='--', alpha=0.7)
                plt.axvline(x=num_patches, color='red', linestyle='--', alpha=0.7)
            
            # 保存
            filename = f"layer_{i+1:02d}_{layer_name.replace('.', '_').replace('/', '_')}.png"
            filepath = os.path.join(output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✅ 保存: {filename}")
            
        except Exception as e:
            print(f"❌ 创建 {layer_name} 可视化失败: {e}")

def save_analysis_results(results, output_dir):
    """保存分析结果"""
    import json
    
    # 准备保存的数据
    save_data = {}
    
    for method_name, result in results.items():
        if result is not None:
            # 转换numpy数组为列表以便JSON序列化
            save_result = {}
            for key, value in result.items():
                if key == 'attention_weights':
                    save_result[key] = {}
                    for layer_name, layer_data in value.items():
                        save_result[key][layer_name] = {
                            'shape': layer_data['attention_matrix'].shape,
                            'analysis': layer_data.get('analysis', {}),
                            'num_heads': layer_data.get('num_heads', 'unknown')
                        }
                else:
                    save_result[key] = value
            
            save_data[method_name] = save_result
    
    # 保存JSON文件
    json_path = os.path.join(output_dir, "complete_analysis_results.json")
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(save_data, f, indent=2, ensure_ascii=False)
    
    print(f"📄 分析结果已保存: {json_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PaliGemma完整注意力分析')
    parser.add_argument('--model_path', type=str, 
                       default='/home/<USER>/dataset/X/models/PaliGemma',
                       help='PaliGemma模型路径')
    parser.add_argument('--image_path', type=str,
                       default='/home/<USER>/dataset/X/coke.png',
                       help='输入图片路径')
    parser.add_argument('--text_prompt', type=str,
                       default='coke',
                       help='文本提示')
    parser.add_argument('--output_dir', type=str,
                       default=None,
                       help='输出目录 (默认自动生成)')
    parser.add_argument('--device', type=str,
                       default='cuda:0',
                       help='设备 (cuda:0, cpu等)')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.image_path):
        print(f"❌ 错误: 图片文件不存在: {args.image_path}")
        return 1
    
    if not os.path.exists(args.model_path):
        print(f"❌ 错误: 模型路径不存在: {args.model_path}")
        return 1
    
    # 创建输出目录
    if args.output_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"paligemma_attention_analysis_{timestamp}"
    else:
        output_dir = args.output_dir
    
    # 运行分析
    success = run_attention_analysis(
        args.model_path, 
        args.image_path, 
        args.text_prompt, 
        output_dir, 
        args.device
    )
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
