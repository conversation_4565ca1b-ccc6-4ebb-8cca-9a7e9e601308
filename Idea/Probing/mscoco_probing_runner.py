#!/usr/bin/env python3
"""
MSCOCO数据集的PaliGemma Linear Probing实验
专门适配WebDataset格式的MSCOCO数据集
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from transformers import PaliGemmaForConditionalGeneration, PaliGemmaProcessor
from PIL import Image
import numpy as np
from sklearn.metrics import accuracy_score, classification_report
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import logging
import tarfile
import webdataset as wds
import io
import re
from collections import Counter

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MSCOCOWebDataset(Dataset):
    """MSCOCO WebDataset格式的Probing数据集"""
    
    def __init__(self, data_dir, processor, max_samples=None, split='train'):
        self.data_dir = data_dir
        self.processor = processor
        self.split = split
        self.max_samples = max_samples
        
        # 加载数据
        self.samples = []
        self.labels = []
        self.label_to_idx = {}
        self.idx_to_label = {}
        
        self._load_webdataset()
        self._create_classification_labels()

    def apply_class_mapping(self, label_to_idx):
        """使用给定的类别映射重新为样本打标签，保证train/val一致"""
        self.label_to_idx = dict(label_to_idx)
        self.idx_to_label = {idx: word for word, idx in self.label_to_idx.items()}
        self.num_classes = len(self.label_to_idx)

        new_samples = []
        for sample in self.samples:
            candidate_labels = []
            for caption in sample['captions']:
                words = re.findall(r'\b[a-zA-Z]+\b', caption.lower())
                for word in words:
                    if word in self.label_to_idx:
                        candidate_labels.append(word)
            
            if candidate_labels:
                # 选择最常见的候选标签
                label_counts = Counter(candidate_labels)
                most_common_label = label_counts.most_common(1)[0][0]
                sample['label'] = self.label_to_idx[most_common_label]
                new_samples.append(sample)
        
        dropped = len(self.samples) - len(new_samples)
        if dropped > 0:
            logger.info(f"验证集因未命中任何类别被丢弃样本数: {dropped}")
        self.samples = new_samples
        
    def _load_webdataset(self):
        """加载WebDataset格式的数据"""
        split_dir = os.path.join(self.data_dir, self.split)
        tar_files = [f for f in os.listdir(split_dir) if f.endswith('.tar')]
        tar_files.sort()
        
        logger.info(f"找到 {len(tar_files)} 个tar文件")
        
        sample_count = 0
        for tar_file in tar_files:
            if self.max_samples and sample_count >= self.max_samples:
                break
                
            tar_path = os.path.join(split_dir, tar_file)
            logger.info(f"处理文件: {tar_file}")
            
            with tarfile.open(tar_path, 'r') as tar:
                # 获取所有成员
                members = tar.getmembers()
                
                # 按照文件名分组 (s0000000.jpg, s0000000.txt)
                file_groups = {}
                for member in members:
                    if member.isfile():
                        base_name = member.name.split('.')[0]
                        if base_name not in file_groups:
                            file_groups[base_name] = {}
                        
                        if member.name.endswith('.jpg'):
                            file_groups[base_name]['image'] = member
                        elif member.name.endswith('.txt'):
                            file_groups[base_name]['text'] = member
                
                # 处理每个样本
                for base_name, files in file_groups.items():
                    if self.max_samples and sample_count >= self.max_samples:
                        break
                        
                    if 'image' in files and 'text' in files:
                        try:
                            # 读取图像
                            img_data = tar.extractfile(files['image']).read()
                            image = Image.open(io.BytesIO(img_data)).convert('RGB')
                            
                            # 读取文本
                            text_data = tar.extractfile(files['text']).read().decode('utf-8')
                            captions = [line.strip() for line in text_data.split('\n') if line.strip()]
                            
                            # 存储样本
                            self.samples.append({
                                'image': image,
                                'captions': captions,
                                'sample_id': f"{tar_file}_{base_name}"
                            })
                            
                            sample_count += 1
                            
                        except Exception as e:
                            logger.warning(f"处理样本 {base_name} 时出错: {e}")
                            continue
        
        logger.info(f"成功加载 {len(self.samples)} 个样本")
    
    def _create_classification_labels(self):
        """从图像描述中创建分类标签（专注于20个主要视觉物体）"""
        # 预定义20个主要视觉物体类别（COCO数据集中常见的物体）
        TARGET_OBJECTS = [
            'person', 'car', 'dog', 'cat', 'horse', 'bike', 'bicycle', 
            'bus', 'truck', 'boat', 'plane', 'airplane',
            'chair', 'table', 'bed', 'couch', 'sofa',
            'tv', 'laptop', 'phone', 'book', 'cup', 'bottle',
            'bowl', 'cake', 'pizza', 'sandwich',
            'bird', 'cow', 'sheep', 'elephant', 'bear', 'zebra', 'giraffe',
            'motorcycle', 'train', 'fire', 'hydrant', 'stop', 'sign'
        ]
        
        # 统计所有词汇出现频次
        all_words = []
        for sample in self.samples:
            for caption in sample['captions']:
                words = re.findall(r'\b[a-zA-Z]+\b', caption.lower())
                all_words.extend(words)
        
        word_counts = Counter(all_words)
        self._word_counts = word_counts
        
        # 从目标物体中选择在数据集中出现频次足够的类别
        valid_objects = []
        for obj in TARGET_OBJECTS:
            if word_counts.get(obj, 0) >= 15:  # 至少出现15次
                valid_objects.append(obj)
        
        # 如果有效物体不足20个，补充一些高频的常见名词
        if len(valid_objects) < 20:
            # 额外的常见物体词汇
            additional_candidates = [
                'water', 'food', 'man', 'woman', 'child', 'baby',
                'house', 'building', 'street', 'road', 'tree', 'grass',
                'ball', 'umbrella', 'bag', 'clock', 'window', 'door'
            ]
            for candidate in additional_candidates:
                if candidate not in valid_objects and word_counts.get(candidate, 0) >= 15:
                    valid_objects.append(candidate)
                    if len(valid_objects) >= 20:
                        break
        
        # 限制为20个类别
        selected_objects = valid_objects[:20]
        
        # 创建标签映射
        self.label_to_idx = {word: idx for idx, word in enumerate(selected_objects)}
        self.idx_to_label = {idx: word for word, idx in self.label_to_idx.items()}
        self.num_classes = len(selected_objects)

        logger.info(f"创建了 {self.num_classes} 个视觉物体类别")
        logger.info(f"选择的类别: {selected_objects}")

        # 为每个样本分配标签：选择最相关的物体类别
        new_samples = []
        for sample in self.samples:
            candidate_labels = []
            for caption in sample['captions']:
                words = re.findall(r'\b[a-zA-Z]+\b', caption.lower())
                for word in words:
                    if word in self.label_to_idx:
                        candidate_labels.append(word)
            
            if candidate_labels:
                # 选择最常见的候选标签（而不是最稀少的）
                label_counts = Counter(candidate_labels)
                most_common_label = label_counts.most_common(1)[0][0]
                sample['label'] = self.label_to_idx[most_common_label]
                new_samples.append(sample)
        
        dropped = len(self.samples) - len(new_samples)
        if dropped > 0:
            logger.info(f"由于未命中任何类别，丢弃样本数: {dropped}")
        self.samples = new_samples
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        
        # 处理图像 - 使用最小文本输入避免警告
        image = sample['image']

        # 纯视觉特征探针：使用<image>标记但不添加具体文本内容
        inputs = self.processor(
            text="<image>",  # 只提供必需的<image>标记
            images=image,
            return_tensors="pt",
            padding=True,
            truncation=True
        )

        # 移除batch维度
        for key in inputs:
            if inputs[key].dim() > 1:
                inputs[key] = inputs[key].squeeze(0)
        
        return {
            'pixel_values': inputs['pixel_values'],
            'input_ids': inputs['input_ids'],
            'attention_mask': inputs['attention_mask'],
            'label': torch.tensor(sample['label'], dtype=torch.long)
        }

class PaliGemmaProber:
    """PaliGemma线性探测器"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = device
        self.model_path = model_path
        
        # 加载模型和处理器
        logger.info(f"加载模型: {model_path}")
        self.model = PaliGemmaForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32,
            device_map=device
        )
        self.processor = PaliGemmaProcessor.from_pretrained(model_path)
        
        # 冻结模型参数
        for param in self.model.parameters():
            param.requires_grad = False
        
        self.model.eval()
        logger.info("模型加载完成")
    
    def extract_features(self, batch):
        """提取特征"""
        with torch.no_grad():
            # 获取视觉特征
            pixel_values = batch['pixel_values'].to(self.device)
            # 与模型dtype对齐，避免隐式类型转换
            if self.device == 'cuda':
                pixel_values = pixel_values.half()
            
            # 通过vision encoder
            vision_outputs = self.model.vision_tower(pixel_values)
            image_features = vision_outputs.last_hidden_state  # [batch_size, seq_len, hidden_size]
            
            # 改进的池化策略：使用注意力加权池化
            # 学习一个全局查询向量来计算注意力权重
            query = image_features.mean(dim=1, keepdim=True)  # [batch_size, 1, hidden_size]
            attention_scores = torch.matmul(query, image_features.transpose(-2, -1))  # [batch_size, 1, seq_len]
            attention_weights = F.softmax(attention_scores.squeeze(1), dim=-1)  # [batch_size, seq_len]
            
            # 使用注意力权重进行加权平均
            pooled_features = torch.sum(image_features * attention_weights.unsqueeze(-1), dim=1)  # [batch_size, hidden_size]
            
            return pooled_features
    
    def train_probing_classifier(self, train_dataset, val_dataset, num_epochs=10, 
                                batch_size=16, learning_rate=1e-3):
        """训练线性探测分类器"""
        
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_fn)
        
        # 获取特征维度
        sample_batch = next(iter(train_loader))
        sample_features = self.extract_features(sample_batch)
        feature_dim = sample_features.shape[1]
        
        # 创建带正则化的分类器（保持float32，避免half精度下softmax数值不稳定导致NaN）
        classifier = nn.Sequential(
            nn.Dropout(0.3),
            nn.Linear(feature_dim, train_dataset.num_classes)
        ).to(self.device).float()
        
        # 改进的类别权重计算（平衡类别不均衡）
        class_counts = Counter([s['label'] for s in train_dataset.samples])
        total_samples = len(train_dataset.samples)
        
        # 计算每个类别的权重，使用平滑的逆频率权重
        weights = []
        for cls in range(train_dataset.num_classes):
            cnt = class_counts.get(cls, 0)
            if cnt > 0:
                # 使用平滑逆频率：总样本数 / (类别样本数 + 平滑因子)
                weight = total_samples / (cnt + 10)  # 平滑因子避免权重过大
            else:
                weight = 0.0
            weights.append(weight)
        
        class_weights = torch.tensor(weights, dtype=torch.float32, device=self.device)
        # 归一化权重
        if class_weights.sum() > 0:
            class_weights = class_weights / class_weights.mean()

        # 优化器、损失与调度器 - 增强正则化
        optimizer = optim.AdamW(classifier.parameters(), lr=learning_rate, weight_decay=5e-4)
        criterion = nn.CrossEntropyLoss(weight=class_weights if class_weights.sum() > 0 else None)
        # 使用ReduceLROnPlateau调度器
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='max', factor=0.5, patience=3, verbose=True)
        
        # 训练循环
        train_losses = []
        val_accuracies = []
        
        # Early Stopping 参数
        best_val_acc = 0.0
        best_model_state = None
        patience = 10
        patience_counter = 0
        
        logger.info(f"开始训练，特征维度: {feature_dim}, 类别数: {train_dataset.num_classes}")
        
        for epoch in range(num_epochs):
            # 训练阶段
            classifier.train()
            epoch_loss = 0
            
            for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}"):
                # 提取特征并做L2归一化
                features = self.extract_features(batch)
                features = F.normalize(features.float(), p=2, dim=1)
                labels = batch['label'].to(self.device)
                
                # 前向传播
                outputs = classifier(features)
                loss = criterion(outputs, labels)
                
                # 反向传播
                optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(classifier.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_loss += loss.item()
            
            avg_loss = epoch_loss / len(train_loader)
            train_losses.append(avg_loss)
            
            # 验证阶段
            classifier.eval()
            val_predictions = []
            val_labels = []
            
            with torch.no_grad():
                for batch in val_loader:
                    features = self.extract_features(batch)
                    features = F.normalize(features.float(), p=2, dim=1)
                    labels = batch['label'].to(self.device)
                    
                    outputs = classifier(features)
                    predictions = torch.argmax(outputs, dim=1)
                    
                    val_predictions.extend(predictions.cpu().numpy())
                    val_labels.extend(labels.cpu().numpy())
            
            val_accuracy = accuracy_score(val_labels, val_predictions)
            val_accuracies.append(val_accuracy)
            
            # Early Stopping 逻辑
            if val_accuracy > best_val_acc:
                best_val_acc = val_accuracy
                best_model_state = classifier.state_dict().copy()
                patience_counter = 0
                logger.info(f"Epoch {epoch+1}: Loss={avg_loss:.4f}, Val Acc={val_accuracy:.4f} *** New Best ***")
            else:
                patience_counter += 1
                logger.info(f"Epoch {epoch+1}: Loss={avg_loss:.4f}, Val Acc={val_accuracy:.4f} (patience: {patience_counter}/{patience})")
            
            # 如果连续patience个epoch没有改进，则停止训练
            if patience_counter >= patience:
                logger.info(f"Early stopping triggered after {epoch+1} epochs. Best validation accuracy: {best_val_acc:.4f}")
                break
                
            # ReduceLROnPlateau需要传入验证指标
            scheduler.step(val_accuracy)
        
        # 如果找到了更好的模型，则恢复最佳权重
        if best_model_state is not None:
            classifier.load_state_dict(best_model_state)
            logger.info(f"Restored best model with validation accuracy: {best_val_acc:.4f}")
        
        return classifier, train_losses, val_accuracies
    
    def save_results(self, classifier, train_losses, val_accuracies, output_dir, dataset_info):
        """保存训练结果"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存分类器
        torch.save(classifier.state_dict(), os.path.join(output_dir, 'probing_classifier.pth'))
        
        # 保存训练结果
        results = {
            'train_losses': train_losses,
            'val_accuracies': val_accuracies,
            'final_val_accuracy': val_accuracies[-1],
            'dataset_info': dataset_info,
            'timestamp': datetime.now().isoformat()
        }
        
        with open(os.path.join(output_dir, 'training_results.json'), 'w') as f:
            json.dump(results, f, indent=2)
        
        # 绘制训练曲线
        _, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        ax1.plot(train_losses)
        ax1.set_title('Training Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        
        ax2.plot(val_accuracies)
        ax2.set_title('Validation Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'training_curves.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"结果已保存到: {output_dir}")

def collate_fn(batch):
    """自定义collate函数"""
    pixel_values = torch.stack([item['pixel_values'] for item in batch])
    labels = torch.stack([item['label'] for item in batch])
    
    # 处理文本输入（虽然只是<image>标记，但需要保持一致性）
    input_ids = torch.stack([item['input_ids'] for item in batch])
    attention_mask = torch.stack([item['attention_mask'] for item in batch])

    return {
        'pixel_values': pixel_values,
        'input_ids': input_ids,
        'attention_mask': attention_mask,
        'label': labels
    }

def main():
    """主函数"""
    # 配置
    model_path = "/home/<USER>/dataset/X/models/PaliGemma"
    data_dir = "/home/<USER>/dataset/X/Idea/Probing/Datasets/Mscoco"
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    logger.info(f"使用设备: {device}")
    logger.info(f"模型路径: {model_path}")
    logger.info(f"数据路径: {data_dir}")
    
    # 检查路径
    if not os.path.exists(model_path):
        logger.error(f"模型路径不存在: {model_path}")
        return
    
    if not os.path.exists(data_dir):
        logger.error(f"数据路径不存在: {data_dir}")
        return
    
    try:
        # 初始化探测器
        prober = PaliGemmaProber(model_path, device=device)
        
        # 创建数据集
        logger.info("加载训练数据集...")
        train_dataset = MSCOCOWebDataset(
            data_dir=data_dir,
            processor=prober.processor,
            max_samples=2000,  # 限制样本数量以加快训练
            split='train'
        )
        
        logger.info("加载验证数据集...")
        val_dataset = MSCOCOWebDataset(
            data_dir=data_dir,
            processor=prober.processor,
            max_samples=500,
            split='test'
        )
        
        # 确保类别数一致，并基于训练集的映射重新标注验证集
        val_dataset.apply_class_mapping(train_dataset.label_to_idx)

        # 日志：样本数与类别分布
        logger.info(f"训练集样本(有标签): {len(train_dataset)}; 验证集样本(有标签): {len(val_dataset)}")
        train_counts = Counter([s['label'] for s in train_dataset.samples])
        val_counts = Counter([s['label'] for s in val_dataset.samples])
        top_train = sorted(train_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        logger.info(f"训练集Top10类别分布: {[(train_dataset.idx_to_label[k], v) for k, v in top_train]}")
        top_val = sorted(val_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        logger.info(f"验证集Top10类别分布: {[(val_dataset.idx_to_label[k], v) for k, v in top_val]}")
        
        # 训练
        logger.info("开始训练线性探测分类器...")
        classifier, train_losses, val_accuracies = prober.train_probing_classifier(
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            num_epochs=30,
            batch_size=8,  # 如显存允许可增至16
            learning_rate=1e-3  # 线性探测更合适的学习率
        )
        
        # 保存结果
        output_dir = f"results/mscoco_probing_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        dataset_info = {
            'train_samples': len(train_dataset),
            'val_samples': len(val_dataset),
            'num_classes': train_dataset.num_classes,
            'dataset_type': 'mscoco_webdataset',
            'class_names': list(train_dataset.label_to_idx.keys())
        }
        
        prober.save_results(classifier, train_losses, val_accuracies, output_dir, dataset_info)
        
        logger.info(f"实验完成！最终验证准确率: {val_accuracies[-1]:.4f}")
        logger.info(f"结果保存在: {output_dir}")
        
        # 打印类别信息
        logger.info(f"分类类别 ({train_dataset.num_classes}个):")
        for i, (label, idx) in enumerate(train_dataset.label_to_idx.items()):
            if i < 10:  # 只显示前10个
                logger.info(f"  {idx}: {label}")
        
    except Exception as e:
        logger.error(f"实验失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
