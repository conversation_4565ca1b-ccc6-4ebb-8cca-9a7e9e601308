#!/usr/bin/env python3
"""
评估指标模块
用于计算和可视化probing实验的各种评估指标
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    accuracy_score, precision_recall_fscore_support,
    confusion_matrix, classification_report,
    roc_auc_score, roc_curve
)
from sklearn.manifold import TSNE
import pandas as pd
import json
import os
from datetime import datetime

class ProbingEvaluator:
    """Probing实验评估器"""
    
    def __init__(self, class_names=None):
        self.class_names = class_names
        self.results = {}
    
    def compute_classification_metrics(self, y_true, y_pred, y_prob=None):
        """计算分类指标"""
        metrics = {}
        
        # 基础指标
        metrics['accuracy'] = accuracy_score(y_true, y_pred)
        
        # 精确率、召回率、F1分数
        precision, recall, f1, support = precision_recall_fscore_support(
            y_true, y_pred, average='weighted'
        )
        metrics['precision'] = precision
        metrics['recall'] = recall
        metrics['f1_score'] = f1
        
        # 每个类别的指标
        precision_per_class, recall_per_class, f1_per_class, support_per_class = \
            precision_recall_fscore_support(y_true, y_pred, average=None)
        
        metrics['per_class'] = {
            'precision': precision_per_class.tolist(),
            'recall': recall_per_class.tolist(),
            'f1_score': f1_per_class.tolist(),
            'support': support_per_class.tolist()
        }
        
        # AUC (如果提供概率)
        if y_prob is not None:
            try:
                if len(np.unique(y_true)) == 2:  # 二分类
                    metrics['auc'] = roc_auc_score(y_true, y_prob[:, 1])
                else:  # 多分类
                    metrics['auc'] = roc_auc_score(y_true, y_prob, multi_class='ovr')
            except:
                metrics['auc'] = None
        
        return metrics
    
    def plot_confusion_matrix(self, y_true, y_pred, save_path=None):
        """绘制混淆矩阵"""
        cm = confusion_matrix(y_true, y_pred)
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=self.class_names,
                   yticklabels=self.class_names)
        plt.title('Confusion Matrix')
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        return cm
    
    def plot_classification_report(self, y_true, y_pred, save_path=None):
        """绘制分类报告热图"""
        report = classification_report(y_true, y_pred, 
                                     target_names=self.class_names,
                                     output_dict=True)
        
        # 转换为DataFrame
        df = pd.DataFrame(report).iloc[:-1, :].T  # 排除accuracy行
        
        plt.figure(figsize=(8, 6))
        sns.heatmap(df.iloc[:, :-1], annot=True, cmap='RdYlBu_r', 
                   fmt='.2f', cbar_kws={'label': 'Score'})
        plt.title('Classification Report')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_feature_tsne(self, features, labels, save_path=None):
        """绘制特征的t-SNE可视化"""
        # 执行t-SNE降维
        tsne = TSNE(n_components=2, random_state=42, perplexity=30)
        features_2d = tsne.fit_transform(features)
        
        plt.figure(figsize=(10, 8))
        scatter = plt.scatter(features_2d[:, 0], features_2d[:, 1], 
                            c=labels, cmap='tab10', alpha=0.7)
        plt.colorbar(scatter)
        plt.title('t-SNE Visualization of Features')
        plt.xlabel('t-SNE 1')
        plt.ylabel('t-SNE 2')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_roc_curves(self, y_true, y_prob, save_path=None):
        """绘制ROC曲线"""
        n_classes = y_prob.shape[1]
        
        plt.figure(figsize=(10, 8))
        
        if n_classes == 2:  # 二分类
            fpr, tpr, _ = roc_curve(y_true, y_prob[:, 1])
            auc = roc_auc_score(y_true, y_prob[:, 1])
            plt.plot(fpr, tpr, label=f'ROC Curve (AUC = {auc:.2f})')
        else:  # 多分类
            for i in range(n_classes):
                y_true_binary = (y_true == i).astype(int)
                fpr, tpr, _ = roc_curve(y_true_binary, y_prob[:, i])
                auc = roc_auc_score(y_true_binary, y_prob[:, i])
                class_name = self.class_names[i] if self.class_names else f'Class {i}'
                plt.plot(fpr, tpr, label=f'{class_name} (AUC = {auc:.2f})')
        
        plt.plot([0, 1], [0, 1], 'k--', label='Random')
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('ROC Curves')
        plt.legend()
        plt.grid(True)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def analyze_layer_performance(self, layer_results, save_path=None):
        """分析不同层的性能"""
        layers = list(layer_results.keys())
        accuracies = [layer_results[layer]['accuracy'] for layer in layers]
        
        plt.figure(figsize=(12, 6))
        plt.plot(layers, accuracies, 'bo-', linewidth=2, markersize=8)
        plt.xlabel('Layer')
        plt.ylabel('Accuracy')
        plt.title('Probing Accuracy Across Layers')
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        
        # 标注最佳层
        best_layer = layers[np.argmax(accuracies)]
        best_acc = max(accuracies)
        plt.annotate(f'Best: {best_layer}\nAcc: {best_acc:.3f}',
                    xy=(best_layer, best_acc),
                    xytext=(10, 10), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        return best_layer, best_acc
    
    def generate_comprehensive_report(self, results, output_dir):
        """生成综合评估报告"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建HTML报告
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>PaliGemma Probing Results</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .metric {{ margin: 10px 0; }}
                .section {{ margin: 30px 0; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>PaliGemma Linear Probing Results</h1>
                <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="section">
                <h2>Overall Performance</h2>
                <div class="metric">Accuracy: {results.get('accuracy', 'N/A'):.4f}</div>
                <div class="metric">Precision: {results.get('precision', 'N/A'):.4f}</div>
                <div class="metric">Recall: {results.get('recall', 'N/A'):.4f}</div>
                <div class="metric">F1 Score: {results.get('f1_score', 'N/A'):.4f}</div>
            </div>
            
            <div class="section">
                <h2>Per-Class Performance</h2>
                <table>
                    <tr><th>Class</th><th>Precision</th><th>Recall</th><th>F1 Score</th><th>Support</th></tr>
        """
        
        if 'per_class' in results and self.class_names:
            for i, class_name in enumerate(self.class_names):
                if i < len(results['per_class']['precision']):
                    html_content += f"""
                    <tr>
                        <td>{class_name}</td>
                        <td>{results['per_class']['precision'][i]:.4f}</td>
                        <td>{results['per_class']['recall'][i]:.4f}</td>
                        <td>{results['per_class']['f1_score'][i]:.4f}</td>
                        <td>{results['per_class']['support'][i]}</td>
                    </tr>
                    """
        
        html_content += """
                </table>
            </div>
        </body>
        </html>
        """
        
        # 保存HTML报告
        with open(os.path.join(output_dir, 'evaluation_report.html'), 'w') as f:
            f.write(html_content)
        
        # 保存JSON结果
        with open(os.path.join(output_dir, 'evaluation_results.json'), 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"评估报告已保存到: {output_dir}")

def compare_probing_results(results_list, labels, save_path=None):
    """比较多个probing实验的结果"""
    metrics = ['accuracy', 'precision', 'recall', 'f1_score']
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    axes = axes.ravel()
    
    for i, metric in enumerate(metrics):
        values = [results[metric] for results in results_list]
        axes[i].bar(labels, values)
        axes[i].set_title(f'{metric.title()}')
        axes[i].set_ylabel('Score')
        axes[i].tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for j, v in enumerate(values):
            axes[i].text(j, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    # 示例用法
    evaluator = ProbingEvaluator(class_names=['cat', 'dog', 'car', 'airplane', 'bird'])
    
    # 模拟一些结果
    y_true = np.random.randint(0, 5, 100)
    y_pred = np.random.randint(0, 5, 100)
    y_prob = np.random.rand(100, 5)
    
    # 计算指标
    metrics = evaluator.compute_classification_metrics(y_true, y_pred, y_prob)
    print("Classification Metrics:", metrics)
    
    # 生成可视化
    evaluator.plot_confusion_matrix(y_true, y_pred)
    evaluator.plot_classification_report(y_true, y_pred)
