# PaliGemma Linear Probing 实验

这个项目实现了对PaliGemma视觉语言模型的线性探测（Linear Probing）实验，用于评估模型的视觉表示能力。

## 📋 项目概述

Linear Probing是一种评估预训练模型表示质量的方法，通过冻结预训练模型的参数，只训练一个简单的线性分类器来测试模型学到的特征的有用性。

### 🎯 支持的任务
- **图像分类**: 基于COCO数据集的物体分类
- **视觉问答**: 基于VQA 2.0数据集的问答任务
- **快速测试**: 使用合成数据集进行快速验证

### 📊 推荐数据集

根据研究文献，以下数据集按优先级推荐：

1. **COCO 2017** (推荐首选)
   - 适合: 图像分类、物体检测、图像描述
   - 优点: 数据量适中，广泛使用，训练效率高
   - 大小: ~20GB
   - 类别: 80个物体类别

2. **VQA 2.0** (问答任务)
   - 适合: 视觉推理、多模态理解
   - 优点: 测试复杂的视觉语言交互
   - 大小: ~2GB
   - 任务: 视觉问答

3. **Winoground** (高级评估)
   - 适合: 组合理解、细粒度评估
   - 优点: 专门测试视觉语言组合能力
   - 难度: 高

## 🚀 快速开始

### 1. 环境准备

```bash
# 激活conda环境
source /home/<USER>/software/anaconda3/bin/activate spatialvla

# 安装依赖
pip install -r requirements.txt
```

### 2. 快速测试（推荐首次使用）

```bash
# 运行快速测试（使用合成数据）
python run_probing_experiment.py --mode quick
```

这将：
- 创建简单的彩色图像数据集
- 训练一个小型线性分类器
- 验证整个流程是否正常工作
- 大约需要5-10分钟

### 3. 完整实验

```bash
# 首先设置数据集
python run_probing_experiment.py --mode setup

# 运行完整实验
python run_probing_experiment.py --mode full
```

## 📁 项目结构

```
Idea/Probing/
├── paligemma_probing_trainer.py    # 主要训练代码
├── evaluation_metrics.py           # 评估指标和可视化
├── dataset_preparation.py          # 数据集下载和准备
├── run_probing_experiment.py       # 简化的运行脚本
├── config.json                     # 配置文件
├── requirements.txt                # Python依赖
├── README.md                       # 本文件
├── Datasets/                       # 数据集目录
│   ├── COCO/                      # COCO数据集
│   ├── VQA/                       # VQA数据集
│   └── Simple/                    # 简单测试数据集
└── results/                        # 实验结果
    └── experiment_YYYYMMDD_HHMMSS/
        ├── probing_classifier.pth  # 训练好的分类器
        ├── training_results.json   # 训练结果
        └── training_curves.png     # 训练曲线图
```

## ⚙️ 配置说明

编辑 `config.json` 来调整实验参数：

```json
{
  "model_config": {
    "model_path": "/home/<USER>/dataset/X/models/PaliGemma",
    "device": "cuda",
    "batch_size": 16,
    "learning_rate": 1e-3,
    "num_epochs": 10
  }
}
```

### 关键参数说明：
- `batch_size`: 批次大小，根据GPU内存调整
- `learning_rate`: 学习率，通常1e-3效果较好
- `num_epochs`: 训练轮数，10-20轮通常足够
- `device`: 使用的设备，'cuda'或'cpu'

## 📈 实验结果解读

### 训练输出示例：
```
Epoch 1: Loss=2.1234, Val Acc=0.3456
Epoch 2: Loss=1.8765, Val Acc=0.4567
...
Epoch 10: Loss=0.9876, Val Acc=0.7890
```

### 结果文件：
1. **training_curves.png**: 训练损失和验证准确率曲线
2. **training_results.json**: 详细的数值结果
3. **probing_classifier.pth**: 训练好的分类器权重

### 性能基准：
- **随机基线**: 1/类别数 (如80类COCO为1.25%)
- **良好性能**: >50% 准确率
- **优秀性能**: >70% 准确率

## 🔧 高级用法

### 1. 自定义数据集

```python
from paligemma_probing_trainer import COCOProbingDataset

# 创建自定义数据集
dataset = COCOProbingDataset(
    image_dir="path/to/images",
    annotation_file="path/to/annotations.json",
    processor=processor,
    max_samples=1000
)
```

### 2. 多层分析

```python
# 分析不同层的表示能力
for layer_name in ['layer_1', 'layer_2', 'layer_3']:
    # 提取该层特征并训练分类器
    # 比较不同层的性能
```

### 3. 评估指标

```python
from evaluation_metrics import ProbingEvaluator

evaluator = ProbingEvaluator(class_names=class_names)
metrics = evaluator.compute_classification_metrics(y_true, y_pred, y_prob)
evaluator.plot_confusion_matrix(y_true, y_pred)
```

## 🐛 常见问题

### 1. CUDA内存不足
```bash
# 减少批次大小
"batch_size": 8  # 或更小
```

### 2. 数据集下载失败
```bash
# 手动下载COCO数据集
wget http://images.cocodataset.org/zips/train2017.zip
wget http://images.cocodataset.org/annotations/annotations_trainval2017.zip
```

### 3. 模型加载失败
```bash
# 检查模型路径
ls /home/<USER>/dataset/X/models/PaliGemma/
```

## 📚 理论背景

### Linear Probing的原理：
1. **冻结预训练模型**: 保持所有预训练参数不变
2. **提取特征**: 使用模型提取图像的高维特征表示
3. **训练分类器**: 在这些特征上训练简单的线性分类器
4. **评估性能**: 分类器的性能反映了特征的质量

### 为什么使用Linear Probing：
- **评估表示质量**: 测试预训练模型学到的特征是否有用
- **计算效率**: 只需训练少量参数，速度快
- **可解释性**: 结果容易理解和比较
- **标准化评估**: 广泛用于学术研究中

## 🔗 相关资源

- [PaliGemma论文](https://arxiv.org/abs/2407.07726)
- [Linear Probing相关研究](https://arxiv.org/abs/2103.00020)
- [COCO数据集](https://cocodataset.org/)
- [VQA 2.0数据集](https://visualqa.org/)

## 📄 许可证

本项目遵循MIT许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！
