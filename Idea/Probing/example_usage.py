#!/usr/bin/env python3
"""
PaliGemma Probing 使用示例
展示如何使用这个probing训练系统的各种功能
"""

import os
import sys
import json
import torch
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from paligemma_probing_trainer import PaliGemmaProber, COCOProbingDataset
from evaluation_metrics import ProbingEvaluator, compare_probing_results
from dataset_preparation import DatasetDownloader

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def example_1_quick_start():
    """示例1: 快速开始 - 最简单的使用方式"""
    print("=" * 60)
    print("示例1: 快速开始")
    print("=" * 60)
    
    # 检查环境
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 模型路径
    model_path = "/home/<USER>/dataset/X/models/PaliGemma"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型路径不存在: {model_path}")
        return False
    
    try:
        # 1. 初始化探测器
        print("🔄 加载PaliGemma模型...")
        prober = PaliGemmaProber(model_path, device=device)
        print("✅ 模型加载成功")
        
        # 2. 创建简单测试数据
        print("🔄 创建测试数据...")
        from run_probing_experiment import create_simple_dataset
        dataset_dir, images_dir, annotation_file = create_simple_dataset(50)
        
        # 3. 创建数据集
        dataset = COCOProbingDataset(
            image_dir=images_dir,
            annotation_file=annotation_file,
            processor=prober.processor,
            max_samples=50
        )
        
        # 4. 分割数据集
        train_size = int(0.8 * len(dataset))
        val_size = len(dataset) - train_size
        train_dataset, val_dataset = torch.utils.data.random_split(
            dataset, [train_size, val_size]
        )
        
        # 5. 训练
        print("🔄 开始训练...")
        classifier, train_losses, val_accuracies = prober.train_probing_classifier(
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            num_epochs=3,
            batch_size=8,
            learning_rate=1e-3
        )
        
        print(f"✅ 训练完成！最终准确率: {val_accuracies[-1]:.4f}")
        return True
        
    except Exception as e:
        print(f"❌ 示例1失败: {e}")
        return False

def example_2_custom_evaluation():
    """示例2: 自定义评估 - 使用详细的评估指标"""
    print("=" * 60)
    print("示例2: 自定义评估")
    print("=" * 60)
    
    # 模拟一些预测结果
    import numpy as np
    
    # 创建模拟数据
    n_samples = 100
    n_classes = 5
    class_names = ['red', 'green', 'blue', 'yellow', 'purple']
    
    # 模拟真实标签和预测结果
    y_true = np.random.randint(0, n_classes, n_samples)
    y_pred = np.random.randint(0, n_classes, n_samples)
    y_prob = np.random.rand(n_samples, n_classes)
    
    # 让预测结果稍微好一些（不完全随机）
    for i in range(n_samples):
        if np.random.rand() > 0.3:  # 70%的概率预测正确
            y_pred[i] = y_true[i]
            y_prob[i, y_true[i]] = 0.8 + 0.2 * np.random.rand()
    
    # 创建评估器
    evaluator = ProbingEvaluator(class_names=class_names)
    
    # 计算指标
    print("🔄 计算评估指标...")
    metrics = evaluator.compute_classification_metrics(y_true, y_pred, y_prob)
    
    print("📊 评估结果:")
    print(f"  准确率: {metrics['accuracy']:.4f}")
    print(f"  精确率: {metrics['precision']:.4f}")
    print(f"  召回率: {metrics['recall']:.4f}")
    print(f"  F1分数: {metrics['f1_score']:.4f}")
    if metrics['auc']:
        print(f"  AUC: {metrics['auc']:.4f}")
    
    # 生成可视化（在实际环境中会显示图表）
    print("🔄 生成可视化...")
    try:
        # 这些在Jupyter环境中会显示图表
        evaluator.plot_confusion_matrix(y_true, y_pred, save_path="example_confusion_matrix.png")
        evaluator.plot_classification_report(y_true, y_pred, save_path="example_classification_report.png")
        print("✅ 可视化已保存")
    except Exception as e:
        print(f"⚠️  可视化生成失败: {e}")
    
    return metrics

def example_3_compare_experiments():
    """示例3: 比较实验 - 比较不同配置的结果"""
    print("=" * 60)
    print("示例3: 比较实验")
    print("=" * 60)
    
    # 模拟不同实验的结果
    experiments = {
        'Baseline': {
            'accuracy': 0.45,
            'precision': 0.43,
            'recall': 0.45,
            'f1_score': 0.44
        },
        'With Augmentation': {
            'accuracy': 0.52,
            'precision': 0.51,
            'recall': 0.52,
            'f1_score': 0.51
        },
        'Higher LR': {
            'accuracy': 0.48,
            'precision': 0.47,
            'recall': 0.48,
            'f1_score': 0.47
        },
        'More Epochs': {
            'accuracy': 0.55,
            'precision': 0.54,
            'recall': 0.55,
            'f1_score': 0.54
        }
    }
    
    print("📊 实验结果比较:")
    for exp_name, results in experiments.items():
        print(f"  {exp_name}:")
        print(f"    准确率: {results['accuracy']:.4f}")
        print(f"    F1分数: {results['f1_score']:.4f}")
    
    # 找出最佳实验
    best_exp = max(experiments.items(), key=lambda x: x[1]['accuracy'])
    print(f"\n🏆 最佳实验: {best_exp[0]} (准确率: {best_exp[1]['accuracy']:.4f})")
    
    # 生成比较图表
    try:
        results_list = list(experiments.values())
        labels = list(experiments.keys())
        compare_probing_results(results_list, labels, save_path="example_comparison.png")
        print("✅ 比较图表已保存")
    except Exception as e:
        print(f"⚠️  比较图表生成失败: {e}")
    
    return experiments

def example_4_advanced_configuration():
    """示例4: 高级配置 - 展示如何使用配置文件"""
    print("=" * 60)
    print("示例4: 高级配置")
    print("=" * 60)
    
    # 加载配置文件
    config_path = "config.json"
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        print("📋 当前配置:")
        print(f"  模型路径: {config['model_config']['model_path']}")
        print(f"  批次大小: {config['model_config']['batch_size']}")
        print(f"  学习率: {config['model_config']['learning_rate']}")
        print(f"  训练轮数: {config['model_config']['num_epochs']}")
        
        # 展示数据集配置
        print("\n📊 支持的数据集:")
        for dataset_name, dataset_info in config['datasets'].items():
            print(f"  {dataset_name}: {dataset_info['description']}")
            print(f"    推荐用途: {dataset_info['recommended_for']}")
        
        # 展示任务配置
        print("\n🎯 支持的任务:")
        for task_name, task_info in config['probing_tasks'].items():
            print(f"  {task_name}: {task_info['description']}")
            print(f"    难度: {task_info['difficulty']}")
            expected = task_info['expected_performance']
            print(f"    期望性能: {expected['good_performance']:.1%} (良好) - {expected['excellent_performance']:.1%} (优秀)")
        
        return config
    else:
        print(f"❌ 配置文件不存在: {config_path}")
        return None

def example_5_batch_processing():
    """示例5: 批量处理 - 处理多个数据集或配置"""
    print("=" * 60)
    print("示例5: 批量处理")
    print("=" * 60)
    
    # 定义多个实验配置
    experiment_configs = [
        {
            'name': 'Small_Batch',
            'batch_size': 8,
            'learning_rate': 1e-3,
            'num_epochs': 5
        },
        {
            'name': 'Large_Batch',
            'batch_size': 32,
            'learning_rate': 2e-3,
            'num_epochs': 5
        },
        {
            'name': 'Low_LR',
            'batch_size': 16,
            'learning_rate': 5e-4,
            'num_epochs': 8
        }
    ]
    
    print("🔄 批量实验配置:")
    results = {}
    
    for config in experiment_configs:
        print(f"\n实验: {config['name']}")
        print(f"  批次大小: {config['batch_size']}")
        print(f"  学习率: {config['learning_rate']}")
        print(f"  训练轮数: {config['num_epochs']}")
        
        # 模拟实验结果（实际中这里会运行真实的训练）
        import random
        simulated_accuracy = 0.4 + 0.3 * random.random()  # 0.4-0.7之间的随机准确率
        
        results[config['name']] = {
            'accuracy': simulated_accuracy,
            'config': config
        }
        
        print(f"  ✅ 模拟结果: {simulated_accuracy:.4f}")
    
    # 分析最佳配置
    best_config = max(results.items(), key=lambda x: x[1]['accuracy'])
    print(f"\n🏆 最佳配置: {best_config[0]}")
    print(f"  准确率: {best_config[1]['accuracy']:.4f}")
    print(f"  参数: {best_config[1]['config']}")
    
    return results

def main():
    """主函数 - 运行所有示例"""
    print("🚀 PaliGemma Probing 使用示例")
    print("这些示例展示了如何使用probing训练系统的各种功能\n")
    
    # 切换到脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    examples = [
        ("快速开始", example_1_quick_start),
        ("自定义评估", example_2_custom_evaluation),
        ("比较实验", example_3_compare_experiments),
        ("高级配置", example_4_advanced_configuration),
        ("批量处理", example_5_batch_processing)
    ]
    
    results = {}
    
    for name, func in examples:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            result = func()
            results[name] = result
            print(f"✅ {name} 完成")
        except Exception as e:
            print(f"❌ {name} 失败: {e}")
            results[name] = None
    
    print("\n" + "="*60)
    print("📋 示例运行总结:")
    for name, result in results.items():
        status = "✅ 成功" if result is not None else "❌ 失败"
        print(f"  {name}: {status}")
    
    print("\n💡 提示:")
    print("- 运行 python run_probing_experiment.py --mode quick 进行快速测试")
    print("- 运行 python run_probing_experiment.py --mode full 进行完整实验")
    print("- 查看 README.md 了解更多详细信息")

if __name__ == "__main__":
    main()
