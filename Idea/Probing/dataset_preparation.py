#!/usr/bin/env python3
"""
数据集准备脚本
用于下载和准备probing训练所需的数据集
"""

import os
import json
import requests
import zipfile
from tqdm import tqdm
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatasetDownloader:
    """数据集下载器"""
    
    def __init__(self, base_dir="/home/<USER>/dataset/X/Idea/Probing/Datasets"):
        self.base_dir = base_dir
        os.makedirs(base_dir, exist_ok=True)
    
    def download_file(self, url, filepath):
        """下载文件"""
        logger.info(f"下载 {url} 到 {filepath}")
        
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        with open(filepath, 'wb') as f, tqdm(
            desc=os.path.basename(filepath),
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    pbar.update(len(chunk))
    
    def extract_zip(self, zip_path, extract_to):
        """解压ZIP文件"""
        logger.info(f"解压 {zip_path} 到 {extract_to}")
        
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        
        # 删除ZIP文件以节省空间
        os.remove(zip_path)
    
    def prepare_coco_dataset(self):
        """准备COCO数据集"""
        coco_dir = os.path.join(self.base_dir, "COCO")
        os.makedirs(coco_dir, exist_ok=True)
        
        # COCO 2017数据集URLs
        urls = {
            'train_images': 'http://images.cocodataset.org/zips/train2017.zip',
            'val_images': 'http://images.cocodataset.org/zips/val2017.zip',
            'annotations': 'http://images.cocodataset.org/annotations/annotations_trainval2017.zip'
        }
        
        for name, url in urls.items():
            filename = url.split('/')[-1]
            filepath = os.path.join(coco_dir, filename)
            
            if not os.path.exists(filepath.replace('.zip', '')):
                if not os.path.exists(filepath):
                    self.download_file(url, filepath)
                self.extract_zip(filepath, coco_dir)
            else:
                logger.info(f"{name} 已存在，跳过下载")
        
        logger.info("COCO数据集准备完成")
        return coco_dir
    
    def prepare_vqa_dataset(self):
        """准备VQA 2.0数据集"""
        vqa_dir = os.path.join(self.base_dir, "VQA")
        os.makedirs(vqa_dir, exist_ok=True)
        
        # VQA 2.0数据集URLs
        urls = {
            'train_questions': 'https://s3.amazonaws.com/cvmlp/vqa/mscoco/vqa/v2_Questions_Train_mscoco.zip',
            'val_questions': 'https://s3.amazonaws.com/cvmlp/vqa/mscoco/vqa/v2_Questions_Val_mscoco.zip',
            'train_annotations': 'https://s3.amazonaws.com/cvmlp/vqa/mscoco/vqa/v2_Annotations_Train_mscoco.zip',
            'val_annotations': 'https://s3.amazonaws.com/cvmlp/vqa/mscoco/vqa/v2_Annotations_Val_mscoco.zip'
        }
        
        for name, url in urls.items():
            filename = url.split('/')[-1]
            filepath = os.path.join(vqa_dir, filename)
            
            if not os.path.exists(filepath.replace('.zip', '')):
                if not os.path.exists(filepath):
                    self.download_file(url, filepath)
                self.extract_zip(filepath, vqa_dir)
            else:
                logger.info(f"{name} 已存在，跳过下载")
        
        logger.info("VQA数据集准备完成")
        return vqa_dir
    
    def create_sample_dataset(self):
        """创建小样本数据集用于快速测试"""
        sample_dir = os.path.join(self.base_dir, "Sample")
        os.makedirs(sample_dir, exist_ok=True)
        
        # 创建简单的分类数据集
        categories = ['cat', 'dog', 'car', 'airplane', 'bird']
        
        # 创建模拟的注释文件
        annotations = {
            "images": [],
            "annotations": [],
            "categories": []
        }
        
        # 添加类别
        for i, cat_name in enumerate(categories):
            annotations["categories"].append({
                "id": i + 1,
                "name": cat_name,
                "supercategory": "object"
            })
        
        # 创建模拟图像和注释
        for i in range(100):  # 100个样本
            img_id = i + 1
            cat_id = (i % len(categories)) + 1
            
            annotations["images"].append({
                "id": img_id,
                "file_name": f"sample_{img_id:03d}.jpg",
                "width": 224,
                "height": 224
            })
            
            annotations["annotations"].append({
                "id": img_id,
                "image_id": img_id,
                "category_id": cat_id,
                "bbox": [50, 50, 124, 124],
                "area": 124 * 124,
                "iscrowd": 0
            })
        
        # 保存注释文件
        with open(os.path.join(sample_dir, "annotations.json"), 'w') as f:
            json.dump(annotations, f, indent=2)
        
        logger.info(f"样本数据集创建完成: {sample_dir}")
        return sample_dir

def create_config_file():
    """创建配置文件"""
    config = {
        "datasets": {
            "coco": {
                "name": "COCO 2017",
                "description": "Common Objects in Context dataset",
                "tasks": ["object_detection", "image_classification", "captioning"],
                "size": "large",
                "recommended_for": "general_probing"
            },
            "vqa": {
                "name": "VQA 2.0",
                "description": "Visual Question Answering dataset",
                "tasks": ["visual_question_answering"],
                "size": "large",
                "recommended_for": "reasoning_probing"
            },
            "sample": {
                "name": "Sample Dataset",
                "description": "Small sample dataset for quick testing",
                "tasks": ["image_classification"],
                "size": "small",
                "recommended_for": "quick_testing"
            }
        },
        "probing_tasks": {
            "image_classification": {
                "description": "Classify images into predefined categories",
                "metrics": ["accuracy", "precision", "recall", "f1"],
                "difficulty": "easy"
            },
            "object_detection": {
                "description": "Detect and classify objects in images",
                "metrics": ["mAP", "precision", "recall"],
                "difficulty": "medium"
            },
            "visual_question_answering": {
                "description": "Answer questions about image content",
                "metrics": ["accuracy", "BLEU"],
                "difficulty": "hard"
            }
        },
        "model_config": {
            "model_path": "/home/<USER>/dataset/X/models/PaliGemma",
            "device": "cuda",
            "batch_size": 16,
            "learning_rate": 1e-3,
            "num_epochs": 10
        }
    }
    
    config_path = "/home/<USER>/dataset/X/Idea/Probing/config.json"
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    logger.info(f"配置文件已创建: {config_path}")
    return config_path

def main():
    """主函数"""
    logger.info("开始准备数据集...")
    
    downloader = DatasetDownloader()
    
    # 创建配置文件
    create_config_file()
    
    # 创建样本数据集（用于快速测试）
    sample_dir = downloader.create_sample_dataset()
    
    # 询问用户是否下载大型数据集
    print("\n数据集选项:")
    print("1. 仅使用样本数据集（快速测试）")
    print("2. 下载COCO数据集（推荐，约20GB）")
    print("3. 下载VQA数据集（约2GB）")
    print("4. 下载所有数据集")
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice in ['2', '4']:
        try:
            coco_dir = downloader.prepare_coco_dataset()
            logger.info(f"COCO数据集位置: {coco_dir}")
        except Exception as e:
            logger.error(f"下载COCO数据集失败: {e}")
    
    if choice in ['3', '4']:
        try:
            vqa_dir = downloader.prepare_vqa_dataset()
            logger.info(f"VQA数据集位置: {vqa_dir}")
        except Exception as e:
            logger.error(f"下载VQA数据集失败: {e}")
    
    logger.info("数据集准备完成！")
    
    # 打印使用说明
    print("\n使用说明:")
    print("1. 运行 python paligemma_probing_trainer.py 开始训练")
    print("2. 检查 config.json 文件调整参数")
    print("3. 训练结果将保存在 results/ 目录中")

if __name__ == "__main__":
    main()
