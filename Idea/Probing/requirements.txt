# PaliGemma Linear Probing Requirements
# 核心深度学习框架
torch>=2.0.0
torchvision>=0.15.0
transformers>=4.30.0

# 图像处理
Pillow>=9.0.0
opencv-python>=4.5.0

# 数据处理和科学计算
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0

# 可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# 进度条和日志
tqdm>=4.62.0

# 数据集处理
pycocotools>=2.0.4
datasets>=2.0.0
webdataset>=0.2.0

# 降维和聚类
scikit-learn>=1.0.0
umap-learn>=0.5.0

# 网络请求
requests>=2.25.0

# JSON处理（通常已包含在Python标准库中）
# json - 标准库

# 其他工具
scipy>=1.7.0
h5py>=3.1.0

# 可选：加速训练
# accelerate>=0.20.0
# flash-attn>=2.0.0  # 需要特定CUDA版本

# 开发和调试工具（可选）
# jupyter>=1.0.0
# ipywidgets>=7.6.0
# tensorboard>=2.8.0
