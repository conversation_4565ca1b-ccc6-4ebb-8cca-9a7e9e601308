{"datasets": {"coco": {"name": "COCO 2017", "description": "Common Objects in Context dataset", "tasks": ["object_detection", "image_classification", "captioning"], "size": "large", "recommended_for": "general_probing", "num_classes": 80, "download_urls": {"train_images": "http://images.cocodataset.org/zips/train2017.zip", "val_images": "http://images.cocodataset.org/zips/val2017.zip", "annotations": "http://images.cocodataset.org/annotations/annotations_trainval2017.zip"}}, "vqa": {"name": "VQA 2.0", "description": "Visual Question Answering dataset", "tasks": ["visual_question_answering"], "size": "large", "recommended_for": "reasoning_probing", "download_urls": {"train_questions": "https://s3.amazonaws.com/cvmlp/vqa/mscoco/vqa/v2_Questions_Train_mscoco.zip", "val_questions": "https://s3.amazonaws.com/cvmlp/vqa/mscoco/vqa/v2_Questions_Val_mscoco.zip", "train_annotations": "https://s3.amazonaws.com/cvmlp/vqa/mscoco/vqa/v2_Annotations_Train_mscoco.zip", "val_annotations": "https://s3.amazonaws.com/cvmlp/vqa/mscoco/vqa/v2_Annotations_Val_mscoco.zip"}}, "sample": {"name": "Sample Dataset", "description": "Small sample dataset for quick testing", "tasks": ["image_classification"], "size": "small", "recommended_for": "quick_testing", "num_classes": 5}}, "probing_tasks": {"image_classification": {"description": "Classify images into predefined categories", "metrics": ["accuracy", "precision", "recall", "f1"], "difficulty": "easy", "expected_performance": {"random_baseline": 0.0125, "good_performance": 0.5, "excellent_performance": 0.7}}, "object_detection": {"description": "Detect and classify objects in images", "metrics": ["mAP", "precision", "recall"], "difficulty": "medium", "expected_performance": {"random_baseline": 0.01, "good_performance": 0.3, "excellent_performance": 0.5}}, "visual_question_answering": {"description": "Answer questions about image content", "metrics": ["accuracy", "BLEU"], "difficulty": "hard", "expected_performance": {"random_baseline": 0.25, "good_performance": 0.6, "excellent_performance": 0.75}}}, "model_config": {"model_path": "/home/<USER>/dataset/X/models/PaliGemma", "device": "cuda", "batch_size": 16, "learning_rate": 0.001, "num_epochs": 10, "weight_decay": 0.0001, "scheduler": {"type": "cosine", "warmup_epochs": 2}, "early_stopping": {"patience": 3, "min_delta": 0.001}}, "data_config": {"max_samples": {"quick_test": 100, "medium_test": 1000, "full_experiment": 10000}, "train_val_split": 0.8, "image_size": [224, 224], "augmentation": {"enabled": false, "horizontal_flip": 0.5, "rotation": 10, "color_jitter": 0.1}}, "output_config": {"base_dir": "results", "save_model": true, "save_features": false, "generate_plots": true, "generate_report": true}, "logging": {"level": "INFO", "save_logs": true, "log_interval": 10}}