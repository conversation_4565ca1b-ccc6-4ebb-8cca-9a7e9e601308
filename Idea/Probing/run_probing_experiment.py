#!/usr/bin/env python3
"""
简化的Probing实验运行脚本
整合所有功能，提供简单的命令行界面
"""

import os
import sys
import json
import argparse
import torch
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from paligemma_probing_trainer import PaliGemmaProber, COCOProbingDataset
from evaluation_metrics import ProbingEvaluator
from dataset_preparation import DatasetDownloader, create_config_file

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_config(config_path="config.json"):
    """加载配置文件"""
    if not os.path.exists(config_path):
        logger.info("配置文件不存在，创建默认配置...")
        create_config_file()
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    return config

def create_simple_dataset(num_samples=100):
    """创建简单的测试数据集"""
    from PIL import Image
    import numpy as np
    
    # 创建目录
    dataset_dir = "Datasets/Simple"
    images_dir = os.path.join(dataset_dir, "images")
    os.makedirs(images_dir, exist_ok=True)
    
    # 生成简单的彩色图像
    categories = ['red', 'green', 'blue', 'yellow', 'purple']
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (128, 0, 128)]
    
    annotations = {
        "images": [],
        "annotations": [],
        "categories": [{"id": i+1, "name": cat, "supercategory": "color"} 
                      for i, cat in enumerate(categories)]
    }
    
    for i in range(num_samples):
        # 选择颜色
        color_idx = i % len(colors)
        color = colors[color_idx]
        category_id = color_idx + 1
        
        # 创建纯色图像
        img = Image.new('RGB', (224, 224), color)
        img_filename = f"simple_{i:03d}.jpg"
        img_path = os.path.join(images_dir, img_filename)
        img.save(img_path)
        
        # 添加到注释
        annotations["images"].append({
            "id": i + 1,
            "file_name": img_filename,
            "width": 224,
            "height": 224
        })
        
        annotations["annotations"].append({
            "id": i + 1,
            "image_id": i + 1,
            "category_id": category_id,
            "bbox": [0, 0, 224, 224],
            "area": 224 * 224,
            "iscrowd": 0
        })
    
    # 保存注释
    with open(os.path.join(dataset_dir, "annotations.json"), 'w') as f:
        json.dump(annotations, f, indent=2)
    
    logger.info(f"简单数据集已创建: {dataset_dir}")
    return dataset_dir, os.path.join(images_dir), os.path.join(dataset_dir, "annotations.json")

def run_quick_test():
    """运行快速测试"""
    logger.info("开始快速测试...")
    
    # 检查模型路径
    model_path = "/home/<USER>/dataset/X/models/PaliGemma"
    if not os.path.exists(model_path):
        logger.error(f"模型路径不存在: {model_path}")
        return False
    
    # 创建简单数据集
    dataset_dir, images_dir, annotation_file = create_simple_dataset(50)
    
    try:
        # 初始化探测器
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        logger.info(f"使用设备: {device}")
        
        prober = PaliGemmaProber(model_path, device=device)
        
        # 创建数据集
        dataset = COCOProbingDataset(
            image_dir=images_dir,
            annotation_file=annotation_file,
            processor=prober.processor,
            max_samples=50
        )
        
        # 分割数据集
        train_size = int(0.8 * len(dataset))
        val_size = len(dataset) - train_size
        train_dataset, val_dataset = torch.utils.data.random_split(
            dataset, [train_size, val_size]
        )

        # 为子集添加num_classes属性
        train_dataset.num_classes = dataset.num_classes
        val_dataset.num_classes = dataset.num_classes

        # 训练
        logger.info("开始训练...")
        classifier, train_losses, val_accuracies = prober.train_probing_classifier(
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            num_epochs=3,  # 快速测试用较少epoch
            batch_size=8,
            learning_rate=1e-3
        )

        # 保存结果
        output_dir = f"results/quick_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        dataset_info = {
            'total_samples': len(dataset),
            'train_samples': train_size,
            'val_samples': val_size,
            'num_classes': dataset.num_classes,
            'test_type': 'quick_test'
        }
        
        prober.save_results(classifier, train_losses, val_accuracies, output_dir, dataset_info)
        
        logger.info(f"快速测试完成！最终验证准确率: {val_accuracies[-1]:.4f}")
        logger.info(f"结果保存在: {output_dir}")
        
        return True
        
    except Exception as e:
        logger.error(f"快速测试失败: {e}")
        return False

def run_full_experiment(config):
    """运行完整实验"""
    logger.info("开始完整实验...")
    
    model_config = config['model_config']
    model_path = model_config['model_path']
    
    if not os.path.exists(model_path):
        logger.error(f"模型路径不存在: {model_path}")
        return False
    
    # 检查COCO数据集
    coco_dir = "Datasets/COCO"
    if not os.path.exists(coco_dir):
        logger.info("COCO数据集不存在，开始下载...")
        downloader = DatasetDownloader()
        coco_dir = downloader.prepare_coco_dataset()
    
    # 设置路径
    images_dir = os.path.join(coco_dir, "train2017")
    annotation_file = os.path.join(coco_dir, "annotations", "instances_train2017.json")
    
    if not os.path.exists(annotation_file):
        logger.error(f"注释文件不存在: {annotation_file}")
        return False
    
    try:
        # 初始化探测器
        device = model_config.get('device', 'cuda' if torch.cuda.is_available() else 'cpu')
        prober = PaliGemmaProber(model_path, device=device)
        
        # 创建数据集
        dataset = COCOProbingDataset(
            image_dir=images_dir,
            annotation_file=annotation_file,
            processor=prober.processor,
            max_samples=5000  # 限制样本数量
        )
        
        # 分割数据集
        train_size = int(0.8 * len(dataset))
        val_size = len(dataset) - train_size
        train_dataset, val_dataset = torch.utils.data.random_split(
            dataset, [train_size, val_size]
        )
        
        # 训练
        classifier, train_losses, val_accuracies = prober.train_probing_classifier(
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            num_epochs=model_config.get('num_epochs', 10),
            batch_size=model_config.get('batch_size', 16),
            learning_rate=model_config.get('learning_rate', 1e-3)
        )
        
        # 保存结果
        output_dir = f"results/full_experiment_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        dataset_info = {
            'total_samples': len(dataset),
            'train_samples': train_size,
            'val_samples': val_size,
            'num_classes': dataset.num_classes,
            'test_type': 'full_experiment'
        }
        
        prober.save_results(classifier, train_losses, val_accuracies, output_dir, dataset_info)
        
        logger.info(f"完整实验完成！最终验证准确率: {val_accuracies[-1]:.4f}")
        logger.info(f"结果保存在: {output_dir}")
        
        return True
        
    except Exception as e:
        logger.error(f"完整实验失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PaliGemma Probing Experiment')
    parser.add_argument('--mode', choices=['quick', 'full', 'setup'], 
                       default='quick', help='运行模式')
    parser.add_argument('--config', default='config.json', 
                       help='配置文件路径')
    
    args = parser.parse_args()
    
    # 切换到脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    if args.mode == 'setup':
        logger.info("设置数据集...")
        downloader = DatasetDownloader()
        create_config_file()
        downloader.create_sample_dataset()
        logger.info("设置完成！")
        
    elif args.mode == 'quick':
        logger.info("运行快速测试...")
        success = run_quick_test()
        if success:
            print("\n✅ 快速测试成功完成！")
            print("💡 提示: 使用 --mode full 运行完整实验")
        else:
            print("\n❌ 快速测试失败")
            
    elif args.mode == 'full':
        config = load_config(args.config)
        logger.info("运行完整实验...")
        success = run_full_experiment(config)
        if success:
            print("\n✅ 完整实验成功完成！")
        else:
            print("\n❌ 完整实验失败")
    
    print("\n📊 查看结果:")
    print("- 训练曲线: results/*/training_curves.png")
    print("- 详细结果: results/*/training_results.json")
    print("- 模型权重: results/*/probing_classifier.pth")

if __name__ == "__main__":
    main()
