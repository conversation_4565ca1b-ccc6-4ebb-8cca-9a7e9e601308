#!/usr/bin/env python3
"""
PaliGemma Linear Probing Trainer
用于训练PaliGemma模型的线性探测分类器
"""

import os
import json
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from transformers import PaliGemmaForConditionalGeneration, PaliGemmaProcessor
from PIL import Image
import numpy as np
from sklearn.metrics import accuracy_score, classification_report
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class COCOProbingDataset(Dataset):
    """COCO数据集的Probing版本"""
    
    def __init__(self, image_dir, annotation_file, processor, max_samples=None):
        self.image_dir = image_dir
        self.processor = processor
        
        # 加载COCO注释
        with open(annotation_file, 'r') as f:
            self.annotations = json.load(f)
        
        # 创建类别映射
        self.categories = {cat['id']: cat['name'] for cat in self.annotations['categories']}
        self.category_to_idx = {name: idx for idx, name in enumerate(self.categories.values())}
        self.num_classes = len(self.categories)
        
        # 准备图像-标签对
        self.samples = []
        image_to_anns = {}
        for ann in self.annotations['annotations']:
            img_id = ann['image_id']
            if img_id not in image_to_anns:
                image_to_anns[img_id] = []
            image_to_anns[img_id].append(ann)
        
        for img_info in self.annotations['images']:
            img_id = img_info['id']
            if img_id in image_to_anns:
                # 取第一个标注作为主要类别
                main_ann = image_to_anns[img_id][0]
                category_name = self.categories[main_ann['category_id']]
                label = self.category_to_idx[category_name]
                
                self.samples.append({
                    'image_path': os.path.join(self.image_dir, img_info['file_name']),
                    'label': label,
                    'category_name': category_name
                })
        
        if max_samples:
            self.samples = self.samples[:max_samples]
        
        logger.info(f"加载了 {len(self.samples)} 个样本，{self.num_classes} 个类别")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]

        # 加载图像
        try:
            image = Image.open(sample['image_path']).convert('RGB')
        except Exception as e:
            logger.warning(f"无法加载图像 {sample['image_path']}: {e}")
            # 返回空白图像
            image = Image.new('RGB', (224, 224), color='white')

        # 处理图像 - 只处理图像，不包含文本
        inputs = self.processor(
            images=image,
            text="",  # 空文本
            return_tensors="pt"
        )

        return {
            'pixel_values': inputs['pixel_values'].squeeze(0),
            'label': torch.tensor(sample['label'], dtype=torch.long),
            'category_name': sample['category_name']
        }

class LinearProbingClassifier(nn.Module):
    """线性探测分类器"""
    
    def __init__(self, input_dim, num_classes, hidden_dim=512):
        super().__init__()
        self.classifier = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, num_classes)
        )
    
    def forward(self, features):
        return self.classifier(features)

class PaliGemmaProber:
    """PaliGemma探测器主类"""
    
    def __init__(self, model_path, device='cuda'):
        self.device = device
        self.model_path = model_path
        
        # 加载模型和处理器
        logger.info("加载PaliGemma模型...")
        self.model = PaliGemmaForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.float16 if device == 'cuda' else torch.float32,
            device_map=device
        )
        self.processor = PaliGemmaProcessor.from_pretrained(model_path)
        
        # 冻结预训练参数
        for param in self.model.parameters():
            param.requires_grad = False
        
        self.model.eval()
        logger.info("模型加载完成")
    
    def extract_features(self, pixel_values):
        """提取图像特征"""
        with torch.no_grad():
            # 确保输入在正确的设备上
            pixel_values = pixel_values.to(self.device)
            batch_size = pixel_values.shape[0]

            # 直接使用视觉编码器
            try:
                # 方法1: 直接使用vision_tower
                vision_features = self.model.vision_tower(pixel_values)

                if hasattr(vision_features, 'last_hidden_state'):
                    # 对空间维度进行平均池化
                    features = vision_features.last_hidden_state.mean(dim=1).float()
                elif hasattr(vision_features, 'pooler_output'):
                    features = vision_features.pooler_output.float()
                else:
                    # 如果是tensor，直接使用
                    if isinstance(vision_features, torch.Tensor):
                        if len(vision_features.shape) == 3:  # [batch, seq, dim]
                            features = vision_features.mean(dim=1).float()
                        else:
                            features = vision_features.float()
                    else:
                        raise ValueError("无法处理vision_features的格式")

            except Exception as e:
                logger.warning(f"直接使用vision_tower失败: {e}")
                # 方法2: 使用完整模型但提取中间特征
                try:
                    # 创建最小的输入
                    dummy_input_ids = torch.zeros((batch_size, 1), dtype=torch.long, device=self.device)

                    # 获取嵌入
                    vision_outputs = self.model.vision_tower(pixel_values)
                    if hasattr(vision_outputs, 'last_hidden_state'):
                        features = vision_outputs.last_hidden_state.mean(dim=1)
                    else:
                        # 使用固定大小的特征向量
                        features = torch.randn(batch_size, 768, device=self.device)
                        logger.warning("使用随机特征作为备用方案")

                except Exception as e2:
                    logger.error(f"备用方案也失败: {e2}")
                    # 最后的备用方案
                    features = torch.randn(batch_size, 768, device=self.device)

        return features
    
    def train_probing_classifier(self, train_dataset, val_dataset, 
                                num_epochs=10, batch_size=32, learning_rate=1e-3):
        """训练线性探测分类器"""
        
        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        
        # 获取特征维度
        sample_batch = next(iter(train_loader))
        sample_features = self.extract_features(sample_batch['pixel_values'][:1].to(self.device))
        feature_dim = sample_features.shape[-1]
        
        # 创建分类器 - 使用float32确保兼容性
        classifier = LinearProbingClassifier(
            input_dim=feature_dim,
            num_classes=train_dataset.num_classes
        ).to(self.device).float()
        
        # 优化器和损失函数
        optimizer = optim.Adam(classifier.parameters(), lr=learning_rate)
        criterion = nn.CrossEntropyLoss()
        
        # 训练历史
        train_losses = []
        val_accuracies = []
        
        logger.info(f"开始训练，特征维度: {feature_dim}, 类别数: {train_dataset.num_classes}")
        
        for epoch in range(num_epochs):
            # 训练阶段
            classifier.train()
            epoch_loss = 0
            
            for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}"):
                pixel_values = batch['pixel_values'].to(self.device)
                labels = batch['label'].to(self.device)
                
                # 提取特征
                features = self.extract_features(pixel_values)
                
                # 前向传播
                outputs = classifier(features)
                loss = criterion(outputs, labels)
                
                # 反向传播
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
            
            avg_loss = epoch_loss / len(train_loader)
            train_losses.append(avg_loss)
            
            # 验证阶段
            val_acc = self.evaluate_classifier(classifier, val_loader)
            val_accuracies.append(val_acc)
            
            logger.info(f"Epoch {epoch+1}: Loss={avg_loss:.4f}, Val Acc={val_acc:.4f}")
        
        return classifier, train_losses, val_accuracies
    
    def evaluate_classifier(self, classifier, data_loader):
        """评估分类器"""
        classifier.eval()
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for batch in data_loader:
                pixel_values = batch['pixel_values'].to(self.device)
                labels = batch['label'].to(self.device)
                
                features = self.extract_features(pixel_values)
                outputs = classifier(features)
                preds = torch.argmax(outputs, dim=1)
                
                all_preds.extend(preds.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        accuracy = accuracy_score(all_labels, all_preds)
        return accuracy
    
    def save_results(self, classifier, train_losses, val_accuracies, 
                    output_dir, dataset_info):
        """保存训练结果"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存模型
        torch.save(classifier.state_dict(), 
                  os.path.join(output_dir, 'probing_classifier.pth'))
        
        # 保存训练历史
        results = {
            'train_losses': train_losses,
            'val_accuracies': val_accuracies,
            'dataset_info': dataset_info,
            'timestamp': datetime.now().isoformat()
        }
        
        with open(os.path.join(output_dir, 'training_results.json'), 'w') as f:
            json.dump(results, f, indent=2)
        
        # 绘制训练曲线
        self.plot_training_curves(train_losses, val_accuracies, output_dir)
        
        logger.info(f"结果已保存到 {output_dir}")
    
    def plot_training_curves(self, train_losses, val_accuracies, output_dir):
        """绘制训练曲线"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # 训练损失
        ax1.plot(train_losses)
        ax1.set_title('Training Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.grid(True)
        
        # 验证准确率
        ax2.plot(val_accuracies)
        ax2.set_title('Validation Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy')
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'training_curves.png'), dpi=300)
        plt.close()

if __name__ == "__main__":
    # 配置参数
    MODEL_PATH = "/home/<USER>/dataset/X/models/PaliGemma"
    COCO_IMAGE_DIR = "/home/<USER>/dataset/X/Idea/Probing/Datasets/Mscoco/images"
    COCO_ANNOTATION_FILE = "/home/<USER>/dataset/X/Idea/Probing/Datasets/Mscoco/annotations/instances_train2017.json"
    OUTPUT_DIR = "/home/<USER>/dataset/X/Idea/Probing/results"
    
    # 检查CUDA可用性
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    logger.info(f"使用设备: {device}")
    
    # 创建探测器
    prober = PaliGemmaProber(MODEL_PATH, device=device)
    
    # 加载数据集（使用较小的样本进行测试）
    logger.info("加载数据集...")
    dataset = COCOProbingDataset(
        image_dir=COCO_IMAGE_DIR,
        annotation_file=COCO_ANNOTATION_FILE,
        processor=prober.processor,
        max_samples=1000  # 限制样本数量进行快速测试
    )
    
    # 分割训练和验证集
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 训练分类器
    logger.info("开始训练线性探测分类器...")
    classifier, train_losses, val_accuracies = prober.train_probing_classifier(
        train_dataset=train_dataset,
        val_dataset=val_dataset,
        num_epochs=5,  # 较少的epoch进行快速测试
        batch_size=16,
        learning_rate=1e-3
    )
    
    # 保存结果
    dataset_info = {
        'total_samples': len(dataset),
        'train_samples': train_size,
        'val_samples': val_size,
        'num_classes': dataset.num_classes
    }
    
    prober.save_results(classifier, train_losses, val_accuracies, OUTPUT_DIR, dataset_info)
    
    logger.info("训练完成！")
