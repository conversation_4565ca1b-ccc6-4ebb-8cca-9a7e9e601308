# SpatialVLA项目OXE数据处理全流程分析

## 1. 项目概述

SpatialVLA是一个专注于空间理解的视觉-语言-动作模型，基于Gemma2语言模型和PaliGemma视觉模型构建。该项目特别强调3D空间感知能力，使用定制的OXE数据处理流程来支持空间推理任务。

## 2. OXE数据集配置架构

### 2.1 核心配置文件

SpatialVLA项目中OXE数据处理的核心文件：
- `data/oxe/configs.py` - 数据集配置定义
- `data/oxe/mixtures.py` - 数据混合策略
- `data/oxe/transforms.py` - 数据变换函数
- `data/oxe/__init__.py` - 数据集工具函数
- `data/dataset.py` - 主数据集类

### 2.2 数据集配置结构

SpatialVLA使用与OpenVLA相似但更详细的配置格式：

```python
OXE_DATASET_CONFIGS = {
    "fractal20220817_data/0.1.0": {
        "image_obs_keys": {"primary": "image", "secondary": None, "wrist": None},
        "depth_obs_keys": {"primary": None, "secondary": None, "wrist": None},
        "state_obs_keys": ["base_pose_tool_reached", "gripper_closed"],
        "state_encoding": StateEncoding.POS_QUAT,
        "action_encoding": ActionEncoding.EEF_POS,
    }
}
```

**与OpenVLA的区别：**
- 数据集名称包含明确的版本号（如`/0.1.0`、`/1.0.0`）
- 更精细的版本控制和数据集管理

## 3. 空间感知专用数据混合策略

### 3.1 SpatialVLA专用混合配置

#### 3.1.1 OXE Spatial VLA Plus（第一阶段）
```python
"oxe_spatial_vla_plus": [
    ("fractal20220817_data/0.1.0", 0.***********),      # Google RT-1数据
    ("kuka/0.1.0", 0.4),                                # 降权重：缺乏文本提示
    ("bridge_orig/1.0.0", 1.0),                         # Bridge V2原始版本
    ("taco_play/0.1.0", 2.0),                           # TACO Play数据
    ("jaco_play/0.1.0", 1.0),                           # Jaco Play数据（质量一般）
    ("berkeley_cable_routing/0.1.0", 1.0),              # 伯克利电缆路由
    ("roboturk/0.1.0", 2.0),                            # RoboTurk数据
    ("viola/0.1.0", 2.0),                               # VIOLA数据
    ("berkeley_autolab_ur5/0.1.0", 2.0),                # 伯克利UR5数据
    ("toto/0.1.0", 1.0),                                # TOTO数据
    ("language_table/0.1.0", 0.1),                      # 语言表格数据（低权重）
    # ... 更多空间相关数据集
]
```

#### 3.1.2 OXE Spatial VLA Plus Stage 2（第二阶段）
```python
"oxe_spatial_vla_plus_stage2": [
    ("fractal20220817_data/0.1.0", 0.***********),
    ("kuka/0.1.0", 0.4),
    ("bridge_orig/1.0.0", 1.0),
    ("taco_play/0.1.0", 2.0),
    ("jaco_play/0.1.0", 1.0),
    ("berkeley_cable_routing/0.1.0", 1.0),
    ("roboturk/0.1.0", 2.0),
    ("viola/0.1.0", 2.0),
    ("berkeley_autolab_ur5/0.1.0", 2.0),
    ("toto/0.1.0", 0.5),                                # 降权重：质量较低
    ("berkeley_fanuc_manipulation/0.1.0", 0.5),         # 降权重：质量较低
    ("fmb_dataset/1.0.0", 0.2),                         # 降权重：偏好调整
    ("dobbe/0.0.1", 0.2),                               # 降权重：质量一般
    # 注意：第二阶段移除了droid数据集
]
```

### 3.2 权重调整策略

**第二阶段的关键调整：**
- `toto`: 1.0 → 0.5（质量较低）
- `berkeley_fanuc_manipulation`: 2.0 → 0.5（质量较低）
- `fmb_dataset`: 1.0 → 0.2（偏好调整）
- `droid`: 完全移除（在第二阶段训练中）

## 4. 数据加载与预处理流程

### 4.1 主数据集类

```python
class SpatialVLADataset:
    def __init__(self, data_root_dir, data_mix, ...):
        if self.data_mix in OXE_NAMED_MIXTURES:
            mixture_spec = OXE_NAMED_MIXTURES[self.data_mix]
        else:
            mixture_spec = [(os.path.join(self.data_mix, "1.0.0"), 1.0)]
        
        per_dataset_kwargs, weights = get_oxe_dataset_kwargs_and_weights(
            self.data_root_dir,
            mixture_spec,
            load_camera_views=("primary",),
            load_depth=False,
            load_proprio=False,                          # 注意：不加载本体感受信息
            load_language=True,
            action_proprio_normalization_type=NormalizationType.BOUNDS_Q99,
        )
```

**关键配置差异：**
- `load_proprio=False`: SpatialVLA不使用本体感受信息
- `action_proprio_normalization_type=NormalizationType.BOUNDS_Q99`: 使用99分位数边界归一化

### 4.2 空间感知专用变换

SpatialVLA包含专门的空间变换函数：

```python
def spatialvla_dataset_transform(trajectory: Dict[str, Any]) -> Dict[str, Any]:
    import tensorflow_graphics.geometry.transformation as tft
    
    # 计算旋转角度和轴
    angle = tf.norm(trajectory["action"][:, 3:6], axis=-1, keepdims=True)
    axis = trajectory["action"][:, 3:6] / (angle + 1e-6)
    
    # 转换为欧拉角
    trajectory["action"] = tf.concat(
        (
            trajectory["action"][:, :3],                 # 位置
            tft.euler.from_axis_angle(axis=axis, angle=angle),  # 轴角到欧拉角
            invert_gripper_actions(                      # 反转夹爪动作
                tf.clip_by_value(trajectory["action"][:, -1:], 0, 1)
            ),
        ),
        axis=-1,
    )
    return trajectory
```

## 5. 训练集成

### 5.1 预训练脚本配置

在`train/spatialvla_pretrain.py`中：

```python
@dataclass
class DataTrainingArguments:
    data_root_dir: Optional[str] = field(
        default="datasets/open-x-embodiment",
        metadata={"help": "The root directory of the dataset."},
    )
    data_mix: Optional[str] = field(
        default="bridge",
        metadata={"help": "The name of the dataset mixture."},
    )
    max_seq_length: Optional[int] = field(
        default=2048,
        metadata={"help": "The maximum total input sequence length after tokenization."},
    )
    shuffle_buffer_size: Optional[int] = field(
        default=1000_000,
        metadata={"help": "The shuffle buffer size for the dataset."},
    )
    intrinsic_config_path: Path = field(
        default="scripts/intrinsics.json",
        metadata={"help": "path to the intrinsic config file."},
    )
```

### 5.2 模型配置

```python
@dataclass
class ModelArguments:
    vision_zoe_path: Optional[str] = field(
        default=None,
        metadata={"help": "Path to pretrained model or identifier for zoe model."},
    )
    use_vision_zoe: bool = field(
        default=True, 
        metadata={"help": "Set to True to use vision zoe model."},
    )
    freeze_llm_embed: bool = field(
        default=True, 
        metadata={"help": "Set to True to freeze the LLM embeddings."},
    )
```

## 6. 空间感知特性

### 6.1 3D视觉处理

- **Vision ZOE集成**: 使用ZOE模型进行深度估计和3D理解
- **空间Token**: 专门的空间表示token用于3D推理
- **Ego3D补丁**: 支持第一人称3D视角处理

### 6.2 相机内参配置

SpatialVLA使用详细的相机内参配置文件：

```json
// scripts/intrinsics.json
{
    "dataset_name": {
        "camera_matrix": [...],
        "distortion_coeffs": [...],
        "image_size": [width, height]
    }
}
```

## 7. 数据质量优化

### 7.1 分阶段训练策略

1. **第一阶段**: 使用完整的`oxe_spatial_vla_plus`混合
2. **第二阶段**: 使用优化的`oxe_spatial_vla_plus_stage2`混合
   - 移除低质量数据集
   - 调整权重比例
   - 专注于高质量空间数据

### 7.2 数据集质量标注

在混合配置中明确标注数据集质量：
```python
("jaco_play/0.1.0", 1.0),           # not so good
("toto/0.1.0", 0.5),                # NOTE: low quality
("berkeley_fanuc_manipulation/0.1.0", 0.5),  # NOTE: low quality
```

## 8. 关键技术特性

1. **空间专用设计**: 专门针对3D空间理解任务优化
2. **分阶段训练**: 两阶段训练策略提高模型质量
3. **精细版本控制**: 明确的数据集版本管理
4. **质量驱动**: 基于数据质量的权重调整策略
5. **3D视觉集成**: 深度集成ZOE等3D视觉模型

## 9. 与OpenVLA的主要差异

1. **数据集版本**: 明确的版本号管理
2. **本体感受**: 不使用本体感受信息
3. **归一化方式**: 使用99分位数边界归一化
4. **空间专用变换**: 专门的空间感知数据变换
5. **分阶段策略**: 两阶段训练优化数据质量

SpatialVLA的数据处理流程体现了对空间理解任务的专门优化，通过精心设计的数据混合策略和变换函数，为3D空间感知提供了强有力的数据支持。
